import 'package:cloud_firestore/cloud_firestore.dart';

class DynamicBookingService {
  static final DynamicBookingService _instance = DynamicBookingService._internal();
  factory DynamicBookingService() => _instance;
  DynamicBookingService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Collections
  static const String bookingsCollection = 'hotel_bookings';
  static const String roomsCollection = 'hotel_rooms';
  static const String guestsCollection = 'hotel_guests';

  /// Get real-time bookings for a vendor
  Stream<List<Map<String, dynamic>>> getVendorBookingsStream(String vendorId) {
    return _firestore
        .collection(bookingsCollection)
        .where('vendorId', isEqualTo: vendorId)
        .orderBy('bookingDate', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
    });
  }

  /// Get bookings by status
  Future<List<Map<String, dynamic>>> getBookingsByStatus(String vendorId, String status) async {
    try {
      final query = await _firestore
          .collection(bookingsCollection)
          .where('vendorId', isEqualTo: vendorId)
          .where('status', isEqualTo: status)
          .orderBy('checkInDate')
          .get();

      return query.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      print('Error getting bookings by status: $e');
      return [];
    }
  }

  /// Get today's check-ins
  Future<List<Map<String, dynamic>>> getTodayCheckIns(String vendorId) async {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = DateTime(today.year, today.month, today.day, 23, 59, 59);

    try {
      final query = await _firestore
          .collection(bookingsCollection)
          .where('vendorId', isEqualTo: vendorId)
          .where('checkInDate', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
          .where('checkInDate', isLessThanOrEqualTo: Timestamp.fromDate(endOfDay))
          .where('status', isEqualTo: 'confirmed')
          .get();

      return query.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      print('Error getting today check-ins: $e');
      return [];
    }
  }

  /// Get today's check-outs
  Future<List<Map<String, dynamic>>> getTodayCheckOuts(String vendorId) async {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = DateTime(today.year, today.month, today.day, 23, 59, 59);

    try {
      final query = await _firestore
          .collection(bookingsCollection)
          .where('vendorId', isEqualTo: vendorId)
          .where('checkOutDate', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
          .where('checkOutDate', isLessThanOrEqualTo: Timestamp.fromDate(endOfDay))
          .where('status', isEqualTo: 'checked_in')
          .get();

      return query.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      print('Error getting today check-outs: $e');
      return [];
    }
  }

  /// Update booking status
  Future<bool> updateBookingStatus(String bookingId, String status) async {
    try {
      await _firestore
          .collection(bookingsCollection)
          .doc(bookingId)
          .update({
        'status': status,
        'updatedAt': Timestamp.now(),
        if (status == 'checked_in') 'actualCheckInTime': Timestamp.now(),
        if (status == 'checked_out') 'actualCheckOutTime': Timestamp.now(),
      });
      return true;
    } catch (e) {
      print('Error updating booking status: $e');
      return false;
    }
  }

  /// Check in guest
  Future<bool> checkInGuest(String bookingId, {String? aadharNumber}) async {
    try {
      final updates = <String, dynamic>{
        'status': 'checked_in',
        'actualCheckInTime': Timestamp.now(),
        'updatedAt': Timestamp.now(),
      };

      if (aadharNumber != null) {
        updates['aadharNumber'] = aadharNumber;
        updates['aadharVerified'] = true;
        updates['aadharVerificationTime'] = Timestamp.now();
      }

      await _firestore
          .collection(bookingsCollection)
          .doc(bookingId)
          .update(updates);

      return true;
    } catch (e) {
      print('Error checking in guest: $e');
      return false;
    }
  }

  /// Check out guest
  Future<bool> checkOutGuest(String bookingId) async {
    try {
      await _firestore
          .collection(bookingsCollection)
          .doc(bookingId)
          .update({
        'status': 'checked_out',
        'actualCheckOutTime': Timestamp.now(),
        'updatedAt': Timestamp.now(),
      });

      return true;
    } catch (e) {
      print('Error checking out guest: $e');
      return false;
    }
  }

  /// Verify Aadhar for existing booking
  Future<bool> verifyAadhar(String bookingId, String aadharNumber) async {
    try {
      await _firestore
          .collection(bookingsCollection)
          .doc(bookingId)
          .update({
        'aadharNumber': aadharNumber,
        'aadharVerified': true,
        'aadharVerificationTime': Timestamp.now(),
        'updatedAt': Timestamp.now(),
      });

      return true;
    } catch (e) {
      print('Error verifying Aadhar: $e');
      return false;
    }
  }

  /// Get room availability
  Future<Map<String, dynamic>> getRoomAvailability(String hotelId) async {
    try {
      // Get all rooms for the hotel
      final roomsQuery = await _firestore
          .collection(roomsCollection)
          .where('hotelId', isEqualTo: hotelId)
          .get();

      final totalRooms = roomsQuery.docs.length;
      
      // Get current bookings
      final today = DateTime.now();
      final bookingsQuery = await _firestore
          .collection(bookingsCollection)
          .where('hotelId', isEqualTo: hotelId)
          .where('status', whereIn: ['confirmed', 'checked_in'])
          .where('checkInDate', isLessThanOrEqualTo: Timestamp.fromDate(today))
          .where('checkOutDate', isGreaterThan: Timestamp.fromDate(today))
          .get();

      final occupiedRooms = bookingsQuery.docs.length;
      final availableRooms = totalRooms - occupiedRooms;
      final occupancyRate = totalRooms > 0 ? (occupiedRooms / totalRooms) * 100 : 0.0;

      return {
        'totalRooms': totalRooms,
        'occupiedRooms': occupiedRooms,
        'availableRooms': availableRooms,
        'occupancyRate': occupancyRate,
      };
    } catch (e) {
      print('Error getting room availability: $e');
      return {
        'totalRooms': 0,
        'occupiedRooms': 0,
        'availableRooms': 0,
        'occupancyRate': 0.0,
      };
    }
  }

  /// Get booking analytics
  Future<Map<String, dynamic>> getBookingAnalytics(String vendorId) async {
    try {
      final now = DateTime.now();
      final startOfMonth = DateTime(now.year, now.month, 1);
      final endOfMonth = DateTime(now.year, now.month + 1, 0, 23, 59, 59);

      // Monthly bookings
      final monthlyQuery = await _firestore
          .collection(bookingsCollection)
          .where('vendorId', isEqualTo: vendorId)
          .where('bookingDate', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfMonth))
          .where('bookingDate', isLessThanOrEqualTo: Timestamp.fromDate(endOfMonth))
          .get();

      // Total revenue this month
      double monthlyRevenue = 0.0;
      for (final doc in monthlyQuery.docs) {
        final data = doc.data();
        monthlyRevenue += (data['totalAmount'] ?? 0.0).toDouble();
      }

      // Total bookings
      final totalQuery = await _firestore
          .collection(bookingsCollection)
          .where('vendorId', isEqualTo: vendorId)
          .get();

      double totalRevenue = 0.0;
      for (final doc in totalQuery.docs) {
        final data = doc.data();
        totalRevenue += (data['totalAmount'] ?? 0.0).toDouble();
      }

      return {
        'monthlyBookings': monthlyQuery.docs.length,
        'totalBookings': totalQuery.docs.length,
        'monthlyRevenue': monthlyRevenue,
        'totalRevenue': totalRevenue,
        'averageBookingValue': totalQuery.docs.isNotEmpty 
            ? totalRevenue / totalQuery.docs.length 
            : 0.0,
      };
    } catch (e) {
      print('Error getting booking analytics: $e');
      return {
        'monthlyBookings': 0,
        'totalBookings': 0,
        'monthlyRevenue': 0.0,
        'totalRevenue': 0.0,
        'averageBookingValue': 0.0,
      };
    }
  }

  /// Search bookings
  Future<List<Map<String, dynamic>>> searchBookings(String vendorId, String searchTerm) async {
    try {
      // Search by customer name, phone, or room number
      final query = await _firestore
          .collection(bookingsCollection)
          .where('vendorId', isEqualTo: vendorId)
          .get();

      final results = <Map<String, dynamic>>[];
      
      for (final doc in query.docs) {
        final data = doc.data();
        data['id'] = doc.id;
        
        final customerName = (data['customerName'] ?? '').toString().toLowerCase();
        final customerPhone = (data['customerPhone'] ?? '').toString().toLowerCase();
        final roomNumber = (data['roomNumber'] ?? '').toString().toLowerCase();
        final searchLower = searchTerm.toLowerCase();
        
        if (customerName.contains(searchLower) ||
            customerPhone.contains(searchLower) ||
            roomNumber.contains(searchLower)) {
          results.add(data);
        }
      }

      return results;
    } catch (e) {
      print('Error searching bookings: $e');
      return [];
    }
  }

  /// Get guest history
  Future<List<Map<String, dynamic>>> getGuestHistory(String vendorId, String guestId) async {
    try {
      final query = await _firestore
          .collection(bookingsCollection)
          .where('vendorId', isEqualTo: vendorId)
          .where('customerId', isEqualTo: guestId)
          .orderBy('bookingDate', descending: true)
          .get();

      return query.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      print('Error getting guest history: $e');
      return [];
    }
  }

  /// Create new booking (from vendor side)
  Future<String?> createBooking({
    required String vendorId,
    required String hotelId,
    required String customerName,
    required String customerEmail,
    required String customerPhone,
    required String roomNumber,
    required String roomType,
    required DateTime checkInDate,
    required DateTime checkOutDate,
    required int numberOfGuests,
    required double totalAmount,
    String? specialRequests,
  }) async {
    try {
      final booking = {
        'vendorId': vendorId,
        'hotelId': hotelId,
        'customerName': customerName,
        'customerEmail': customerEmail,
        'customerPhone': customerPhone,
        'roomNumber': roomNumber,
        'roomType': roomType,
        'checkInDate': Timestamp.fromDate(checkInDate),
        'checkOutDate': Timestamp.fromDate(checkOutDate),
        'numberOfGuests': numberOfGuests,
        'totalAmount': totalAmount,
        'specialRequests': specialRequests,
        'status': 'confirmed',
        'bookingDate': Timestamp.now(),
        'source': 'vendor_app',
        'aadharVerified': false,
        'paymentStatus': 'pending',
      };

      final docRef = await _firestore
          .collection(bookingsCollection)
          .add(booking);

      return docRef.id;
    } catch (e) {
      print('Error creating booking: $e');
      return null;
    }
  }

  /// Cancel booking
  Future<bool> cancelBooking(String bookingId, String reason) async {
    try {
      await _firestore
          .collection(bookingsCollection)
          .doc(bookingId)
          .update({
        'status': 'cancelled',
        'cancellationReason': reason,
        'cancellationTime': Timestamp.now(),
        'updatedAt': Timestamp.now(),
      });

      return true;
    } catch (e) {
      print('Error cancelling booking: $e');
      return false;
    }
  }

  /// Update booking details
  Future<bool> updateBooking(String bookingId, Map<String, dynamic> updates) async {
    try {
      updates['updatedAt'] = Timestamp.now();
      
      await _firestore
          .collection(bookingsCollection)
          .doc(bookingId)
          .update(updates);

      return true;
    } catch (e) {
      print('Error updating booking: $e');
      return false;
    }
  }
}
