import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'config/app_theme.dart';
import 'screens/auth/hotel_splash_screen.dart';
import 'providers/hotel_vendor_provider.dart';
import 'services/vendor_auth_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  runApp(const HotelVendorApp());
}

class HotelVendorApp extends StatelessWidget {
  const HotelVendorApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => HotelVendorProvider()),
      ],
      child: MaterialApp(
        title: 'Hotel Vendor',
        theme: AppTheme.lightTheme,
        debugShowCheckedModeBanner: false,
        home: const HotelSplashScreen(),
      ),
    );
  }
}
