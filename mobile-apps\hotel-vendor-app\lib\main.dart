import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'config/app_theme.dart';
import 'screens/auth/hotel_splash_screen.dart';
import 'providers/hotel_vendor_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  try {
    await Firebase.initializeApp(
      options: const FirebaseOptions(
        apiKey:
            "AIzaSyBqJVJBjcwjFqXgQHQHQHQHQHQHQHQHQHQ", // Replace with your API key
        authDomain:
            "hotel-vendor-app.firebaseapp.com", // Replace with your domain
        projectId: "hotel-vendor-app", // Replace with your project ID
        storageBucket:
            "hotel-vendor-app.appspot.com", // Replace with your bucket
        messagingSenderId: "123456789012", // Replace with your sender ID
        appId:
            "1:123456789012:web:abcdef123456789012345678", // Replace with your app ID
      ),
    );
    if (kDebugMode) print('Firebase initialized successfully');
  } catch (e) {
    if (kDebugMode) print('Firebase initialization failed: $e');
  }

  runApp(const HotelVendorApp());
}

class HotelVendorApp extends StatelessWidget {
  const HotelVendorApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => HotelVendorProvider()),
      ],
      child: MaterialApp(
        title: 'Hotel Vendor',
        theme: AppTheme.lightTheme,
        debugShowCheckedModeBanner: false,
        home: const HotelSplashScreen(),
      ),
    );
  }
}
