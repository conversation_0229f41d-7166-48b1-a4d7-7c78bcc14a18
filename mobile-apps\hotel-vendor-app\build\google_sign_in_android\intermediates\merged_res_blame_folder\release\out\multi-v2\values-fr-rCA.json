{"logs": [{"outputFile": "io.flutter.plugins.googlesignin.google_sign_in_android-mergeReleaseResources-24:/values-fr-rCA/values-fr-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1e971c28d382ad1e133e73e513ebed36\\transformed\\jetified-play-services-base-18.0.1\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,299,475,601,706,873,1002,1119,1228,1419,1527,1708,1840,1996,2171,2240,2300", "endColumns": "101,175,125,104,166,128,116,108,190,107,180,131,155,174,68,59,79", "endOffsets": "298,474,600,705,872,1001,1118,1227,1418,1526,1707,1839,1995,2170,2239,2299,2379"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "778,884,1064,1194,1303,1474,1607,1728,2002,2197,2309,2494,2630,2790,2969,3042,3106", "endColumns": "105,179,129,108,170,132,120,112,194,111,184,135,159,178,72,63,83", "endOffsets": "879,1059,1189,1298,1469,1602,1723,1836,2192,2304,2489,2625,2785,2964,3037,3101,3185"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\59cf662b2b8d845940ff382134f3256f\\transformed\\core-1.13.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,778", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,874"}, "to": {"startLines": "2,3,4,5,6,7,8,27", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,3190", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,3286"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e012be8d6b500547b7a96b06eb02d118\\transformed\\jetified-play-services-basement-18.2.0\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "156", "endOffsets": "355"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1841", "endColumns": "160", "endOffsets": "1997"}}]}, {"outputFile": "io.flutter.plugins.googlesignin.google_sign_in_android-merged_res-26:/values-fr-rCA/values-fr-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1e971c28d382ad1e133e73e513ebed36\\transformed\\jetified-play-services-base-18.0.1\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,299,475,601,706,873,1002,1119,1228,1419,1527,1708,1840,1996,2171,2240,2300", "endColumns": "101,175,125,104,166,128,116,108,190,107,180,131,155,174,68,59,79", "endOffsets": "298,474,600,705,872,1001,1118,1227,1418,1526,1707,1839,1995,2170,2239,2299,2379"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "778,884,1064,1194,1303,1474,1607,1728,2002,2197,2309,2494,2630,2790,2969,3042,3106", "endColumns": "105,179,129,108,170,132,120,112,194,111,184,135,159,178,72,63,83", "endOffsets": "879,1059,1189,1298,1469,1602,1723,1836,2192,2304,2489,2625,2785,2964,3037,3101,3185"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\59cf662b2b8d845940ff382134f3256f\\transformed\\core-1.13.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,778", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,874"}, "to": {"startLines": "2,3,4,5,6,7,8,27", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,3190", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,3286"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e012be8d6b500547b7a96b06eb02d118\\transformed\\jetified-play-services-basement-18.2.0\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "156", "endOffsets": "355"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1841", "endColumns": "160", "endOffsets": "1997"}}]}]}