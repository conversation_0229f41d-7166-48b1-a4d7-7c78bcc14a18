import 'package:cloud_firestore/cloud_firestore.dart';

// Vendor/Hotel Owner Model
class HotelVendor {
  final String id;
  final String firstName;
  final String lastName;
  final String email;
  final String phone;
  final String businessName;
  final String businessRegistrationNumber;
  final String panNumber;
  final String gstNumber;
  final VendorAddress address;
  final List<String> hotelIds;
  final VendorStatus status;
  final DateTime createdAt;
  final DateTime? verifiedAt;
  final BankDetails? bankDetails;

  HotelVendor({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.phone,
    required this.businessName,
    required this.businessRegistrationNumber,
    required this.panNumber,
    required this.gstNumber,
    required this.address,
    required this.hotelIds,
    required this.status,
    required this.createdAt,
    this.verifiedAt,
    this.bankDetails,
  });

  String get fullName => '$firstName $lastName';

  factory HotelVendor.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return HotelVendor(
      id: doc.id,
      firstName: data['firstName'] ?? '',
      lastName: data['lastName'] ?? '',
      email: data['email'] ?? '',
      phone: data['phone'] ?? '',
      businessName: data['businessName'] ?? '',
      businessRegistrationNumber: data['businessRegistrationNumber'] ?? '',
      panNumber: data['panNumber'] ?? '',
      gstNumber: data['gstNumber'] ?? '',
      address: VendorAddress.fromMap(data['address'] ?? {}),
      hotelIds: List<String>.from(data['hotelIds'] ?? []),
      status: VendorStatus.values.firstWhere(
        (e) => e.toString() == 'VendorStatus.${data['status']}',
        orElse: () => VendorStatus.pending,
      ),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      verifiedAt: data['verifiedAt'] != null 
          ? (data['verifiedAt'] as Timestamp).toDate() 
          : null,
      bankDetails: data['bankDetails'] != null 
          ? BankDetails.fromMap(data['bankDetails']) 
          : null,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'firstName': firstName,
      'lastName': lastName,
      'email': email,
      'phone': phone,
      'businessName': businessName,
      'businessRegistrationNumber': businessRegistrationNumber,
      'panNumber': panNumber,
      'gstNumber': gstNumber,
      'address': address.toMap(),
      'hotelIds': hotelIds,
      'status': status.toString().split('.').last,
      'createdAt': Timestamp.fromDate(createdAt),
      'verifiedAt': verifiedAt != null ? Timestamp.fromDate(verifiedAt!) : null,
      'bankDetails': bankDetails?.toMap(),
    };
  }
}

// Vendor Status Enum
enum VendorStatus {
  pending,     // Waiting for verification
  verified,    // Documents verified, can add hotels
  active,      // Has active hotels
  suspended,   // Temporarily suspended
  rejected,    // Application rejected
}

// Vendor Address Model
class VendorAddress {
  final String street;
  final String city;
  final String state;
  final String pincode;
  final String country;

  VendorAddress({
    required this.street,
    required this.city,
    required this.state,
    required this.pincode,
    required this.country,
  });

  String get fullAddress => '$street, $city, $state $pincode, $country';

  factory VendorAddress.fromMap(Map<String, dynamic> map) {
    return VendorAddress(
      street: map['street'] ?? '',
      city: map['city'] ?? '',
      state: map['state'] ?? '',
      pincode: map['pincode'] ?? '',
      country: map['country'] ?? 'India',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'street': street,
      'city': city,
      'state': state,
      'pincode': pincode,
      'country': country,
    };
  }
}

// Bank Details Model
class BankDetails {
  final String accountHolderName;
  final String accountNumber;
  final String ifscCode;
  final String bankName;
  final String branchName;

  BankDetails({
    required this.accountHolderName,
    required this.accountNumber,
    required this.ifscCode,
    required this.bankName,
    required this.branchName,
  });

  factory BankDetails.fromMap(Map<String, dynamic> map) {
    return BankDetails(
      accountHolderName: map['accountHolderName'] ?? '',
      accountNumber: map['accountNumber'] ?? '',
      ifscCode: map['ifscCode'] ?? '',
      bankName: map['bankName'] ?? '',
      branchName: map['branchName'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'accountHolderName': accountHolderName,
      'accountNumber': accountNumber,
      'ifscCode': ifscCode,
      'bankName': bankName,
      'branchName': branchName,
    };
  }
}

// Hotel Registration Model
class HotelRegistration {
  final String id;
  final String vendorId;
  final String hotelName;
  final String description;
  final HotelAddress address;
  final List<String> amenities;
  final List<String> images;
  final HotelType hotelType;
  final int starRating;
  final List<RoomType> roomTypes;
  final HotelPolicies policies;
  final ContactInfo contactInfo;
  final HotelStatus status;
  final DateTime createdAt;
  final DateTime? approvedAt;

  HotelRegistration({
    required this.id,
    required this.vendorId,
    required this.hotelName,
    required this.description,
    required this.address,
    required this.amenities,
    required this.images,
    required this.hotelType,
    required this.starRating,
    required this.roomTypes,
    required this.policies,
    required this.contactInfo,
    required this.status,
    required this.createdAt,
    this.approvedAt,
  });

  factory HotelRegistration.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return HotelRegistration(
      id: doc.id,
      vendorId: data['vendorId'] ?? '',
      hotelName: data['hotelName'] ?? '',
      description: data['description'] ?? '',
      address: HotelAddress.fromMap(data['address'] ?? {}),
      amenities: List<String>.from(data['amenities'] ?? []),
      images: List<String>.from(data['images'] ?? []),
      hotelType: HotelType.values.firstWhere(
        (e) => e.toString() == 'HotelType.${data['hotelType']}',
        orElse: () => HotelType.hotel,
      ),
      starRating: data['starRating'] ?? 3,
      roomTypes: (data['roomTypes'] as List? ?? [])
          .map((room) => RoomType.fromMap(room))
          .toList(),
      policies: HotelPolicies.fromMap(data['policies'] ?? {}),
      contactInfo: ContactInfo.fromMap(data['contactInfo'] ?? {}),
      status: HotelStatus.values.firstWhere(
        (e) => e.toString() == 'HotelStatus.${data['status']}',
        orElse: () => HotelStatus.pending,
      ),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      approvedAt: data['approvedAt'] != null 
          ? (data['approvedAt'] as Timestamp).toDate() 
          : null,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'vendorId': vendorId,
      'hotelName': hotelName,
      'description': description,
      'address': address.toMap(),
      'amenities': amenities,
      'images': images,
      'hotelType': hotelType.toString().split('.').last,
      'starRating': starRating,
      'roomTypes': roomTypes.map((room) => room.toMap()).toList(),
      'policies': policies.toMap(),
      'contactInfo': contactInfo.toMap(),
      'status': status.toString().split('.').last,
      'createdAt': Timestamp.fromDate(createdAt),
      'approvedAt': approvedAt != null ? Timestamp.fromDate(approvedAt!) : null,
    };
  }
}

// Hotel Address Model
class HotelAddress {
  final String street;
  final String city;
  final String state;
  final String pincode;
  final String country;
  final double? latitude;
  final double? longitude;
  final String? landmark;

  HotelAddress({
    required this.street,
    required this.city,
    required this.state,
    required this.pincode,
    required this.country,
    this.latitude,
    this.longitude,
    this.landmark,
  });

  String get fullAddress => '$street, $city, $state $pincode, $country';

  factory HotelAddress.fromMap(Map<String, dynamic> map) {
    return HotelAddress(
      street: map['street'] ?? '',
      city: map['city'] ?? '',
      state: map['state'] ?? '',
      pincode: map['pincode'] ?? '',
      country: map['country'] ?? 'India',
      latitude: map['latitude']?.toDouble(),
      longitude: map['longitude']?.toDouble(),
      landmark: map['landmark'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'street': street,
      'city': city,
      'state': state,
      'pincode': pincode,
      'country': country,
      'latitude': latitude,
      'longitude': longitude,
      'landmark': landmark,
    };
  }
}

// Hotel Type Enum
enum HotelType {
  hotel,
  resort,
  guesthouse,
  hostel,
  apartment,
  villa,
  homestay,
}

// Hotel Status Enum
enum HotelStatus {
  pending,     // Waiting for approval
  approved,    // Approved and live
  rejected,    // Application rejected
  suspended,   // Temporarily suspended
  inactive,    // Vendor deactivated
}

// Room Type Model
class RoomType {
  final String name;
  final String description;
  final int maxOccupancy;
  final double basePrice;
  final List<String> amenities;
  final int totalRooms;

  RoomType({
    required this.name,
    required this.description,
    required this.maxOccupancy,
    required this.basePrice,
    required this.amenities,
    required this.totalRooms,
  });

  factory RoomType.fromMap(Map<String, dynamic> map) {
    return RoomType(
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      maxOccupancy: map['maxOccupancy'] ?? 2,
      basePrice: map['basePrice']?.toDouble() ?? 0.0,
      amenities: List<String>.from(map['amenities'] ?? []),
      totalRooms: map['totalRooms'] ?? 1,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'description': description,
      'maxOccupancy': maxOccupancy,
      'basePrice': basePrice,
      'amenities': amenities,
      'totalRooms': totalRooms,
    };
  }
}

// Hotel Policies Model
class HotelPolicies {
  final String checkInTime;
  final String checkOutTime;
  final String cancellationPolicy;
  final bool petsAllowed;
  final bool smokingAllowed;
  final int minimumAge;

  HotelPolicies({
    required this.checkInTime,
    required this.checkOutTime,
    required this.cancellationPolicy,
    required this.petsAllowed,
    required this.smokingAllowed,
    required this.minimumAge,
  });

  factory HotelPolicies.fromMap(Map<String, dynamic> map) {
    return HotelPolicies(
      checkInTime: map['checkInTime'] ?? '14:00',
      checkOutTime: map['checkOutTime'] ?? '11:00',
      cancellationPolicy: map['cancellationPolicy'] ?? 'Free cancellation up to 24 hours before check-in',
      petsAllowed: map['petsAllowed'] ?? false,
      smokingAllowed: map['smokingAllowed'] ?? false,
      minimumAge: map['minimumAge'] ?? 18,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'checkInTime': checkInTime,
      'checkOutTime': checkOutTime,
      'cancellationPolicy': cancellationPolicy,
      'petsAllowed': petsAllowed,
      'smokingAllowed': smokingAllowed,
      'minimumAge': minimumAge,
    };
  }
}

// Contact Info Model
class ContactInfo {
  final String email;
  final String phone;
  final String? website;
  final String? fax;

  ContactInfo({
    required this.email,
    required this.phone,
    this.website,
    this.fax,
  });

  factory ContactInfo.fromMap(Map<String, dynamic> map) {
    return ContactInfo(
      email: map['email'] ?? '',
      phone: map['phone'] ?? '',
      website: map['website'],
      fax: map['fax'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'email': email,
      'phone': phone,
      'website': website,
      'fax': fax,
    };
  }
}
