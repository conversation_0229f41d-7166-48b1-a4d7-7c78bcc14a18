import 'package:flutter/material.dart';
import '../../config/app_theme.dart';
import '../../services/mock_services.dart';

class DynamicBookingsScreen extends StatefulWidget {
  const DynamicBookingsScreen({super.key});

  @override
  State<DynamicBookingsScreen> createState() => _DynamicBookingsScreenState();
}

class _DynamicBookingsScreenState extends State<DynamicBookingsScreen> {
  final _mockService = MockHotelRegistrationService();
  String _selectedFilter = 'all';
  List<Map<String, dynamic>> _bookings = [];
  bool _isLoading = false;
  final String _vendorId = 'current_vendor_id'; // TODO: Get from auth

  @override
  void initState() {
    super.initState();
    _loadBookings();
  }

  Future<void> _loadBookings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      List<Map<String, dynamic>> bookings;

      if (_selectedFilter == 'all') {
        // Load all bookings
        bookings =
            await _mockService.getBookingsByStatus(_vendorId, 'confirmed');
        final checkedIn =
            await _mockService.getBookingsByStatus(_vendorId, 'checked_in');
        final checkedOut =
            await _mockService.getBookingsByStatus(_vendorId, 'checked_out');
        bookings.addAll(checkedIn);
        bookings.addAll(checkedOut);
      } else {
        bookings =
            await _mockService.getBookingsByStatus(_vendorId, _selectedFilter);
      }

      setState(() {
        _bookings = bookings;
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading bookings: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Booking Management'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadBookings,
          ),
        ],
      ),
      body: Column(
        children: [
          // Booking Status Summary
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: _buildStatusCard('All', _bookings.length, Colors.blue),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildStatusCard(
                    'Confirmed',
                    _bookings.where((b) => b['status'] == 'confirmed').length,
                    Colors.orange,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildStatusCard(
                    'Checked In',
                    _bookings.where((b) => b['status'] == 'checked_in').length,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildStatusCard(
                    'Checked Out',
                    _bookings.where((b) => b['status'] == 'checked_out').length,
                    Colors.grey,
                  ),
                ),
              ],
            ),
          ),

          // Filter Chips
          Container(
            height: 50,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: ListView(
              scrollDirection: Axis.horizontal,
              children: [
                _buildFilterChip('all', 'All'),
                _buildFilterChip('confirmed', 'Confirmed'),
                _buildFilterChip('checked_in', 'Checked In'),
                _buildFilterChip('checked_out', 'Checked Out'),
              ],
            ),
          ),

          // Bookings List
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _bookings.isEmpty
                    ? _buildEmptyState()
                    : ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: _bookings.length,
                        itemBuilder: (context, index) {
                          final booking = _bookings[index];
                          return _buildBookingCard(booking);
                        },
                      ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showAddBookingDialog,
        backgroundColor: AppTheme.primaryColor,
        icon: const Icon(Icons.add, color: Colors.white),
        label: const Text('Add Booking', style: TextStyle(color: Colors.white)),
      ),
    );
  }

  Widget _buildStatusCard(String title, int count, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            count.toString(),
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String value, String label) {
    final isSelected = _selectedFilter == value;
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            _selectedFilter = value;
          });
          _loadBookings();
        },
        backgroundColor: Colors.grey[200],
        selectedColor: AppTheme.primaryColor.withOpacity(0.2),
        checkmarkColor: AppTheme.primaryColor,
      ),
    );
  }

  Widget _buildBookingCard(Map<String, dynamic> booking) {
    final status = booking['status'] ?? 'confirmed';
    final statusColor = _getStatusColor(status);

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: AppTheme.primaryColor,
                  child: Text(
                    booking['customerName']?.substring(0, 1).toUpperCase() ??
                        'G',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        booking['customerName'] ?? 'Unknown Guest',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Room ${booking['roomNumber']} - ${booking['roomType']}',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: statusColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: statusColor),
                  ),
                  child: Text(
                    status.toUpperCase().replaceAll('_', ' '),
                    style: TextStyle(
                      color: statusColor,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow('Email', booking['customerEmail'] ?? ''),
            _buildInfoRow('Phone', booking['customerPhone'] ?? ''),
            _buildInfoRow('Guests', '${booking['numberOfGuests'] ?? 1}'),
            _buildInfoRow('Check-in', _formatDate(booking['checkInDate'])),
            _buildInfoRow('Check-out', _formatDate(booking['checkOutDate'])),
            _buildInfoRow('Amount',
                '₹${booking['totalAmount']?.toStringAsFixed(0) ?? '0'}'),
            if (booking['specialRequests']?.isNotEmpty == true)
              _buildInfoRow('Special Requests', booking['specialRequests']),
            const SizedBox(height: 16),
            Row(
              children: [
                if (status == 'confirmed')
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _checkInGuest(booking),
                      icon: const Icon(Icons.login, size: 18),
                      label: const Text('Check In'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                if (status == 'checked_in')
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _checkOutGuest(booking),
                      icon: const Icon(Icons.logout, size: 18),
                      label: const Text('Check Out'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                if (status == 'confirmed' || status == 'checked_in') ...[
                  const SizedBox(width: 8),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _editBooking(booking),
                      icon: const Icon(Icons.edit, size: 18),
                      label: const Text('Edit'),
                    ),
                  ),
                ],
                if (status == 'checked_out')
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _viewBookingDetails(booking),
                      icon: const Icon(Icons.info, size: 18),
                      label: const Text('View Details'),
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 6),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.book_online_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No Bookings Found',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Bookings will appear here once guests make reservations',
            style: TextStyle(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'confirmed':
        return Colors.orange;
      case 'checked_in':
        return Colors.green;
      case 'checked_out':
        return Colors.grey;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.blue;
    }
  }

  String _formatDate(dynamic date) {
    if (date == null) return '';
    if (date is DateTime) {
      return '${date.day}/${date.month}/${date.year}';
    }
    // Handle Firestore Timestamp
    try {
      final dateTime = date.toDate();
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    } catch (e) {
      return date.toString();
    }
  }

  Future<void> _checkInGuest(Map<String, dynamic> booking) async {
    final success = await _mockService.checkInGuest(booking['id']);
    if (success) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${booking['customerName']} checked in successfully'),
          backgroundColor: Colors.green,
        ),
      );
      _loadBookings();
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Failed to check in guest'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _checkOutGuest(Map<String, dynamic> booking) async {
    final success = await _mockService.checkOutGuest(booking['id']);
    if (success) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${booking['customerName']} checked out successfully'),
          backgroundColor: Colors.blue,
        ),
      );
      _loadBookings();
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Failed to check out guest'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _editBooking(Map<String, dynamic> booking) {
    // TODO: Implement edit booking dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Edit Booking - ${booking['customerName']}'),
        content:
            const Text('Edit booking functionality will be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _viewBookingDetails(Map<String, dynamic> booking) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Booking Details - ${booking['customerName']}'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildInfoRow('Booking ID', booking['id'] ?? ''),
              _buildInfoRow('Customer', booking['customerName'] ?? ''),
              _buildInfoRow('Email', booking['customerEmail'] ?? ''),
              _buildInfoRow('Phone', booking['customerPhone'] ?? ''),
              _buildInfoRow(
                  'Room', '${booking['roomNumber']} - ${booking['roomType']}'),
              _buildInfoRow('Guests', '${booking['numberOfGuests'] ?? 1}'),
              _buildInfoRow('Check-in', _formatDate(booking['checkInDate'])),
              _buildInfoRow('Check-out', _formatDate(booking['checkOutDate'])),
              _buildInfoRow('Amount',
                  '₹${booking['totalAmount']?.toStringAsFixed(0) ?? '0'}'),
              _buildInfoRow('Status', booking['status'] ?? ''),
              if (booking['specialRequests']?.isNotEmpty == true)
                _buildInfoRow('Special Requests', booking['specialRequests']),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showSearchDialog() {
    final searchController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Bookings'),
        content: TextField(
          controller: searchController,
          decoration: const InputDecoration(
            labelText: 'Search by name, phone, or room',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (searchController.text.isNotEmpty) {
                setState(() {
                  _isLoading = true;
                });

                final results = await _mockService.searchBookings(
                  _vendorId,
                  searchController.text,
                );

                setState(() {
                  _bookings = results;
                  _isLoading = false;
                });

                Navigator.pop(context);
              }
            },
            child: const Text('Search'),
          ),
        ],
      ),
    );
  }

  void _showAddBookingDialog() {
    // TODO: Implement add booking dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add New Booking'),
        content:
            const Text('Add booking functionality will be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
