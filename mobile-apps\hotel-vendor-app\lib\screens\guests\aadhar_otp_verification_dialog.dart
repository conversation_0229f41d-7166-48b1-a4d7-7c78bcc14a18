import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../config/app_theme.dart';
import '../../services/aadhar_verification_service.dart';

class AadharOtpVerificationDialog extends StatefulWidget {
  final String aadharNumber;
  final Function(Map<String, dynamic>) onVerificationComplete;

  const AadharOtpVerificationDialog({
    super.key,
    required this.aadharNumber,
    required this.onVerificationComplete,
  });

  @override
  State<AadharOtpVerificationDialog> createState() => _AadharOtpVerificationDialogState();
}

class _AadharOtpVerificationDialogState extends State<AadharOtpVerificationDialog> {
  final _aadharService = AadharVerificationService();
  final _otpController = TextEditingController();
  
  bool _isGeneratingOtp = false;
  bool _isVerifyingOtp = false;
  bool _otpGenerated = false;
  String? _transactionId;
  String? _maskedMobile;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _generateOtp();
  }

  @override
  void dispose() {
    _otpController.dispose();
    super.dispose();
  }

  Future<void> _generateOtp() async {
    setState(() {
      _isGeneratingOtp = true;
      _errorMessage = null;
    });

    try {
      final result = await _aadharService.generateAadharOtp(widget.aadharNumber);
      
      if (result['success']) {
        setState(() {
          _otpGenerated = true;
          _transactionId = result['data']['transactionId'];
          _maskedMobile = result['data']['maskedMobile'];
        });
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['data']['message'] ?? 'OTP sent successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        setState(() {
          _errorMessage = result['error'];
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error generating OTP: $e';
      });
    } finally {
      setState(() {
        _isGeneratingOtp = false;
      });
    }
  }

  Future<void> _verifyOtp() async {
    if (_otpController.text.isEmpty || _transactionId == null) return;

    setState(() {
      _isVerifyingOtp = true;
      _errorMessage = null;
    });

    try {
      final result = await _aadharService.verifyAadharOtp(_transactionId!, _otpController.text);
      
      if (result['success']) {
        if (mounted) {
          Navigator.of(context).pop();
          widget.onVerificationComplete(result['data']);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Aadhar verified successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        setState(() {
          _errorMessage = result['error'];
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error verifying OTP: $e';
      });
    } finally {
      setState(() {
        _isVerifyingOtp = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: const BoxConstraints(maxWidth: 500),
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Row(
              children: [
                const Icon(Icons.verified_user, size: 28, color: AppTheme.primaryColor),
                const SizedBox(width: 12),
                const Text(
                  'Aadhar OTP Verification',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 16),
            
            // Aadhar Number Display
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Row(
                children: [
                  const Icon(Icons.credit_card, color: AppTheme.primaryColor),
                  const SizedBox(width: 12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Aadhar Number',
                        style: TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                      Text(
                        _aadharService.formatAadharNumber(widget.aadharNumber),
                        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            
            // OTP Generation Status
            if (_isGeneratingOtp) ...[
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              const Text('Generating OTP...'),
            ] else if (_otpGenerated) ...[
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.green[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green[300]!),
                ),
                child: Column(
                  children: [
                    const Icon(Icons.sms, color: Colors.green, size: 32),
                    const SizedBox(height: 8),
                    const Text(
                      'OTP Sent Successfully!',
                      style: TextStyle(fontWeight: FontWeight.bold, color: Colors.green),
                    ),
                    const SizedBox(height: 4),
                    if (_maskedMobile != null)
                      Text(
                        'OTP sent to: $_maskedMobile',
                        style: const TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                  ],
                ),
              ),
              const SizedBox(height: 24),
              
              // OTP Input
              TextFormField(
                controller: _otpController,
                decoration: const InputDecoration(
                  labelText: 'Enter OTP',
                  hintText: 'Enter 6-digit OTP',
                  prefixIcon: Icon(Icons.lock),
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  LengthLimitingTextInputFormatter(6),
                ],
                maxLength: 6,
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 18, letterSpacing: 2),
                onChanged: (value) {
                  if (value.length == 6) {
                    _verifyOtp();
                  }
                },
              ),
              const SizedBox(height: 16),
              
              // Demo OTP Info
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue[300]!),
                ),
                child: const Row(
                  children: [
                    Icon(Icons.info, color: Colors.blue, size: 20),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'For demo: Use OTP 123456 or 000000',
                        style: TextStyle(fontSize: 12, color: Colors.blue),
                      ),
                    ),
                  ],
                ),
              ),
            ] else if (_errorMessage != null) ...[
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red[300]!),
                ),
                child: Column(
                  children: [
                    const Icon(Icons.error, color: Colors.red, size: 32),
                    const SizedBox(height: 8),
                    Text(
                      _errorMessage!,
                      style: const TextStyle(color: Colors.red),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _generateOtp,
                child: const Text('Retry OTP Generation'),
              ),
            ],
            
            // Error Message
            if (_errorMessage != null && _otpGenerated) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red[300]!),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.error, color: Colors.red, size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _errorMessage!,
                        style: const TextStyle(color: Colors.red, fontSize: 12),
                      ),
                    ),
                  ],
                ),
              ),
            ],
            
            const SizedBox(height: 24),
            
            // Action Buttons
            if (_otpGenerated) ...[
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _isVerifyingOtp ? null : () => Navigator.of(context).pop(),
                      child: const Text('Cancel'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isVerifyingOtp || _otpController.text.length != 6 
                          ? null 
                          : _verifyOtp,
                      child: _isVerifyingOtp
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Text('Verify OTP'),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              
              // Resend OTP
              TextButton(
                onPressed: _isGeneratingOtp ? null : _generateOtp,
                child: const Text('Resend OTP'),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
