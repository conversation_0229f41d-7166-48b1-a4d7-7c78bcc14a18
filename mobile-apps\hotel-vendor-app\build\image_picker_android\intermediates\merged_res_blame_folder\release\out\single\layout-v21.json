[{"merged": "io.flutter.plugins.imagepicker.image_picker_android-merged_res-27:/layout-v21/notification_template_icon_group.xml", "source": "io.flutter.plugins.imagepicker.image_picker_android-core-1.13.1-9:/layout-v21/notification_template_icon_group.xml"}, {"merged": "io.flutter.plugins.imagepicker.image_picker_android-merged_res-27:/layout-v21/notification_template_custom_big.xml", "source": "io.flutter.plugins.imagepicker.image_picker_android-core-1.13.1-9:/layout-v21/notification_template_custom_big.xml"}, {"merged": "io.flutter.plugins.imagepicker.image_picker_android-merged_res-27:/layout-v21/notification_action.xml", "source": "io.flutter.plugins.imagepicker.image_picker_android-core-1.13.1-9:/layout-v21/notification_action.xml"}, {"merged": "io.flutter.plugins.imagepicker.image_picker_android-merged_res-27:/layout-v21/notification_action_tombstone.xml", "source": "io.flutter.plugins.imagepicker.image_picker_android-core-1.13.1-9:/layout-v21/notification_action_tombstone.xml"}]