{"logs": [{"outputFile": "io.flutter.plugins.googlesignin.google_sign_in_android-mergeReleaseResources-24:/values-ml/values-ml.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1e971c28d382ad1e133e73e513ebed36\\transformed\\jetified-play-services-base-18.0.1\\res\\values-ml\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,308,483,618,735,895,1016,1117,1219,1397,1509,1679,1811,1956,2113,2173,2238", "endColumns": "114,174,134,116,159,120,100,101,177,111,169,131,144,156,59,64,87", "endOffsets": "307,482,617,734,894,1015,1116,1218,1396,1508,1678,1810,1955,2112,2172,2237,2325"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "792,911,1090,1229,1350,1514,1639,1744,2010,2192,2308,2482,2618,2767,2928,2992,3061", "endColumns": "118,178,138,120,163,124,104,105,181,115,173,135,148,160,63,68,91", "endOffsets": "906,1085,1224,1345,1509,1634,1739,1845,2187,2303,2477,2613,2762,2923,2987,3056,3148"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\59cf662b2b8d845940ff382134f3256f\\transformed\\core-1.13.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,362,466,569,670,792", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "152,255,357,461,564,665,787,888"}, "to": {"startLines": "2,3,4,5,6,7,8,27", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,362,466,569,670,3153", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "152,255,357,461,564,665,787,3249"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e012be8d6b500547b7a96b06eb02d118\\transformed\\jetified-play-services-basement-18.2.0\\res\\values-ml\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "155", "endOffsets": "350"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1850", "endColumns": "159", "endOffsets": "2005"}}]}, {"outputFile": "io.flutter.plugins.googlesignin.google_sign_in_android-merged_res-26:/values-ml/values-ml.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1e971c28d382ad1e133e73e513ebed36\\transformed\\jetified-play-services-base-18.0.1\\res\\values-ml\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,308,483,618,735,895,1016,1117,1219,1397,1509,1679,1811,1956,2113,2173,2238", "endColumns": "114,174,134,116,159,120,100,101,177,111,169,131,144,156,59,64,87", "endOffsets": "307,482,617,734,894,1015,1116,1218,1396,1508,1678,1810,1955,2112,2172,2237,2325"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "792,911,1090,1229,1350,1514,1639,1744,2010,2192,2308,2482,2618,2767,2928,2992,3061", "endColumns": "118,178,138,120,163,124,104,105,181,115,173,135,148,160,63,68,91", "endOffsets": "906,1085,1224,1345,1509,1634,1739,1845,2187,2303,2477,2613,2762,2923,2987,3056,3148"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\59cf662b2b8d845940ff382134f3256f\\transformed\\core-1.13.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,362,466,569,670,792", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "152,255,357,461,564,665,787,888"}, "to": {"startLines": "2,3,4,5,6,7,8,27", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,362,466,569,670,3153", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "152,255,357,461,564,665,787,3249"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e012be8d6b500547b7a96b06eb02d118\\transformed\\jetified-play-services-basement-18.2.0\\res\\values-ml\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "155", "endOffsets": "350"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1850", "endColumns": "159", "endOffsets": "2005"}}]}]}