// Import and configure the Firebase SDK
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js');

// Initialize the Firebase app in the service worker
firebase.initializeApp({
  apiKey: "AIzaSyAD_X8xZZ0wW7k2jMwV_ytwo8ncd2l0u4I",
  authDomain: "linkinblink-f544a.firebaseapp.com",
  projectId: "linkinblink-f544a",
  storageBucket: "linkinblink-f544a.firebasestorage.app",
  messagingSenderId: "408299360705",
  appId: "1:408299360705:web:7ea9f01eec6c08d6dd5f40"
});

// Retrieve an instance of Firebase Messaging so that it can handle background messages
const messaging = firebase.messaging();

// Handle background messages
messaging.onBackgroundMessage(function(payload) {
  console.log('[firebase-messaging-sw.js] Received background message ', payload);
  
  const notificationTitle = payload.notification.title;
  const notificationOptions = {
    body: payload.notification.body,
    icon: '/icons/Icon-192.png',
    badge: '/icons/Icon-192.png'
  };

  self.registration.showNotification(notificationTitle, notificationOptions);
});
