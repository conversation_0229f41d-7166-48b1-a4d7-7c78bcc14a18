[{"merged": "io.flutter.plugins.imagepicker.image_picker_android-merged_res-27:/layout/custom_dialog.xml", "source": "io.flutter.plugins.imagepicker.image_picker_android-core-1.13.1-9:/layout/custom_dialog.xml"}, {"merged": "io.flutter.plugins.imagepicker.image_picker_android-merged_res-27:/layout/notification_template_part_chronometer.xml", "source": "io.flutter.plugins.imagepicker.image_picker_android-core-1.13.1-9:/layout/notification_template_part_chronometer.xml"}, {"merged": "io.flutter.plugins.imagepicker.image_picker_android-merged_res-27:/layout/ime_secondary_split_test_activity.xml", "source": "io.flutter.plugins.imagepicker.image_picker_android-core-1.13.1-9:/layout/ime_secondary_split_test_activity.xml"}, {"merged": "io.flutter.plugins.imagepicker.image_picker_android-merged_res-27:/layout/ime_base_split_test_activity.xml", "source": "io.flutter.plugins.imagepicker.image_picker_android-core-1.13.1-9:/layout/ime_base_split_test_activity.xml"}, {"merged": "io.flutter.plugins.imagepicker.image_picker_android-merged_res-27:/layout/notification_template_part_time.xml", "source": "io.flutter.plugins.imagepicker.image_picker_android-core-1.13.1-9:/layout/notification_template_part_time.xml"}]