library;
import 'package:firebase_data_connect/firebase_data_connect.dart';
import 'dart:convert';
class DefaultConnector {
  

  static ConnectorConfig connectorConfig = ConnectorConfig(
    'asia-east1',
    'default',
    'customer-app',
  );

  DefaultConnector({required this.dataConnect});
  static DefaultConnector get instance {
    return DefaultConnector(
        dataConnect: FirebaseDataConnect.instanceFor(
            connectorConfig: connectorConfig,
            sdkType: CallerSDKType.generated));
  }

  FirebaseDataConnect dataConnect;
}

