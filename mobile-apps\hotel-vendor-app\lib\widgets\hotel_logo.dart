import 'package:flutter/material.dart';
import '../config/app_theme.dart';

class HotelLogo extends StatelessWidget {
  final double size;
  final bool showText;
  final Color? backgroundColor;

  const HotelLogo({
    super.key,
    this.size = 120,
    this.showText = true,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.white,
        borderRadius: BorderRadius.circular(size * 0.2),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Hotel Building Icon
          Stack(
            alignment: Alignment.center,
            children: [
              // Building base
              Container(
                width: size * 0.5,
                height: size * 0.35,
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              // Building roof
              Positioned(
                top: 0,
                child: Container(
                  width: size * 0.6,
                  height: size * 0.1,
                  decoration: BoxDecoration(
                    color: Colors.orange,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              // Building windows - Row 1
              Positioned(
                top: size * 0.12,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildWindow(size),
                    SizedBox(width: size * 0.03),
                    _buildWindow(size),
                    SizedBox(width: size * 0.03),
                    _buildWindow(size),
                  ],
                ),
              ),
              // Building windows - Row 2
              Positioned(
                top: size * 0.2,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildWindow(size),
                    SizedBox(width: size * 0.03),
                    _buildWindow(size),
                    SizedBox(width: size * 0.03),
                    _buildWindow(size),
                  ],
                ),
              ),
              // Door
              Positioned(
                bottom: 0,
                child: Container(
                  width: size * 0.1,
                  height: size * 0.12,
                  decoration: BoxDecoration(
                    color: Colors.brown[700],
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(2),
                      topRight: Radius.circular(2),
                    ),
                  ),
                ),
              ),
              // Door handle
              Positioned(
                bottom: size * 0.06,
                right: size * 0.18,
                child: Container(
                  width: size * 0.01,
                  height: size * 0.01,
                  decoration: const BoxDecoration(
                    color: Colors.yellow,
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ],
          ),
          if (showText) ...[
            SizedBox(height: size * 0.08),
            // "HOTEL" text
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: size * 0.08,
                vertical: size * 0.02,
              ),
              decoration: BoxDecoration(
                color: Colors.orange,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                'HOTEL',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: size * 0.08,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 1,
                ),
              ),
            ),
            SizedBox(height: size * 0.02),
            // "VENDOR" text
            Text(
              'VENDOR',
              style: TextStyle(
                color: AppTheme.primaryColor,
                fontSize: size * 0.06,
                fontWeight: FontWeight.w600,
                letterSpacing: 2,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildWindow(double size) {
    return Container(
      width: size * 0.06,
      height: size * 0.06,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(1),
        border: Border.all(
          color: Colors.blue[100]!,
          width: 0.5,
        ),
      ),
    );
  }
}

class HotelLogoSmall extends StatelessWidget {
  final double size;
  final Color? iconColor;

  const HotelLogoSmall({
    super.key,
    this.size = 40,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(size * 0.2),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Building
          Container(
            width: size * 0.6,
            height: size * 0.5,
            decoration: BoxDecoration(
              color: iconColor ?? AppTheme.primaryColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          // Roof
          Positioned(
            top: size * 0.2,
            child: Container(
              width: size * 0.7,
              height: size * 0.1,
              color: Colors.orange,
            ),
          ),
          // Windows
          Positioned(
            top: size * 0.35,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(width: size * 0.08, height: size * 0.08, color: Colors.white),
                SizedBox(width: size * 0.05),
                Container(width: size * 0.08, height: size * 0.08, color: Colors.white),
                SizedBox(width: size * 0.05),
                Container(width: size * 0.08, height: size * 0.08, color: Colors.white),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
