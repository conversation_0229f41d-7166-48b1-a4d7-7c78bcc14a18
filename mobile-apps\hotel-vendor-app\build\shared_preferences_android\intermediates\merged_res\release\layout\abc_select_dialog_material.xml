<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2015 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<!--
    This layout file is used by the AlertDialog when displaying a list of items.
    This layout file is inflated and used as the ListView to display the items.
    Assign an ID so its state will be saved/restored.
-->
<view xmlns:android="http://schemas.android.com/apk/res/android"
      xmlns:app="http://schemas.android.com/apk/res-auto"
      android:id="@+id/select_dialog_listview"
      style="@style/Widget.AppCompat.ListView"
      class="androidx.appcompat.app.AlertController$RecycleListView"
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      android:layout_gravity="start"
      android:cacheColorHint="@null"
      android:clipToPadding="false"
      android:divider="?attr/listDividerAlertDialog"
      android:fadingEdge="none"
      android:overScrollMode="ifContentScrolls"
      android:scrollbars="vertical"
      android:textAlignment="viewStart"
      app:paddingBottomNoButtons="@dimen/abc_dialog_list_padding_bottom_no_buttons"
      app:paddingTopNoTitle="@dimen/abc_dialog_list_padding_top_no_title"/>