<svg width="375" height="812" viewBox="0 0 375 812" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Beautiful gradient background -->
  <defs>
    <linearGradient id="bg1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="accent1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f093fb;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#f5576c;stop-opacity:0.8" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="375" height="812" fill="url(#bg1)"/>
  
  <!-- Decorative circles -->
  <circle cx="50" cy="100" r="80" fill="url(#accent1)" opacity="0.3"/>
  <circle cx="320" cy="200" r="60" fill="white" opacity="0.1"/>
  <circle cx="100" cy="700" r="100" fill="url(#accent1)" opacity="0.2"/>
  <circle cx="300" cy="600" r="40" fill="white" opacity="0.15"/>
  
  <!-- Travel icons -->
  <g transform="translate(50, 150)">
    <!-- Hotel icon -->
    <rect x="0" y="20" width="40" height="30" fill="white" opacity="0.2" rx="4"/>
    <rect x="5" y="15" width="30" height="5" fill="white" opacity="0.3" rx="2"/>
    <circle cx="10" cy="30" r="2" fill="white" opacity="0.4"/>
    <circle cx="30" cy="30" r="2" fill="white" opacity="0.4"/>
  </g>
  
  <g transform="translate(280, 500)">
    <!-- Plane icon -->
    <path d="M10 20 L30 15 L35 20 L25 25 L30 30 L20 35 L15 25 L5 30 Z" fill="white" opacity="0.2"/>
  </g>
  
  <!-- Floating elements -->
  <rect x="80" y="300" width="20" height="20" fill="white" opacity="0.1" rx="10" transform="rotate(45 90 310)"/>
  <rect x="250" y="400" width="15" height="15" fill="white" opacity="0.15" rx="7" transform="rotate(30 257 407)"/>
</svg>
