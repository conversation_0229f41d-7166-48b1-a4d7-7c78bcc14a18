"-Xallow-no-source-files" "-classpath" "E:\\Ongoing\\lib\\mobile-apps\\hotel-vendor-app\\build\\firebase_analytics\\intermediates\\compile_r_class_jar\\release\\R.jar;E:\\Ongoing\\lib\\mobile-apps\\hotel-vendor-app\\build\\firebase_core\\intermediates\\compile_library_classes_jar\\release\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ea6fc7880fa3630d1dd5b3da6ef83ab8\\transformed\\jetified-firebase-analytics-22.5.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\883bb8709f886f8e6bc982a30122ea7e\\transformed\\jetified-play-services-measurement-api-22.5.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\79d93cb8e1a4bbbdf5175489ed1b503d\\transformed\\jetified-flutter_embedding_release-1.0.0-cb4b5fff73850b2e42bd4de7cb9a4310a78ac40d.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ebcd352e96605df58b55d7dfa0a339d8\\transformed\\jetified-play-services-measurement-22.5.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b69f201ae23f3106c6f03014c4eb0680\\transformed\\jetified-play-services-measurement-sdk-22.5.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7b05fb3c4a1f6ec4b2bb32d153892c28\\transformed\\jetified-play-services-measurement-impl-22.5.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fb2ec56d29c15e3457bcfab6ea4ea322\\transformed\\jetified-play-services-ads-identifier-18.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\288fa2b33837fed0ecaa00cb495931c9\\transformed\\jetified-play-services-measurement-sdk-api-22.5.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ff75f46608ef0b853234e42d551ef8ec\\transformed\\jetified-play-services-measurement-base-22.5.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\29f0534687226b9435c5380588cd1f2c\\transformed\\jetified-play-services-stats-17.0.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2a753915afe31d4c9872b94b8d091bf0\\transformed\\jetified-firebase-installations-18.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0b6e53d523be00093a06d1261ec4b10e\\transformed\\jetified-firebase-installations-interop-17.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\421619abcaf5a9d9fba14f4334242b71\\transformed\\jetified-play-services-base-18.5.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ea238b426b60abbe5a19b34020417887\\transformed\\jetified-firebase-common-ktx-21.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\77e8232966b3e73227a026eaf2f5d093\\transformed\\jetified-firebase-common-21.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\78c82ed3b21105802501ef152a544907\\transformed\\jetified-window-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a73c2977716c5032d1eeda2aa3016efa\\transformed\\jetified-window-java-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\911f01a5dbf58c3441ef061c8041370e\\transformed\\jetified-ads-adservices-java-1.1.0-beta11-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\84f7cd2a68c6da60e97ce4a1273508c5\\transformed\\jetified-ads-adservices-1.1.0-beta11-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\815c389afaec2b9e5ad6e818291cfb48\\transformed\\jetified-activity-1.8.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6d3a936cccd172a27b9c9de5dd55692a\\transformed\\legacy-support-core-utils-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\97522a3bf92b730870592d9637158bd6\\transformed\\loader-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\eecf3a31c429d8a4700cd7e34061860f\\transformed\\jetified-lifecycle-livedata-core-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\39d5013d04e956b223ab31eee593be92\\transformed\\lifecycle-livedata-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9a3af9325cc77c030edb8295730db573\\transformed\\lifecycle-viewmodel-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfb7a4c6b5798c6306fa2bea9e16a797\\transformed\\lifecycle-livedata-core-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\84c905ba5d5b60ba7bdf9d5d74da33d6\\transformed\\jetified-lifecycle-viewmodel-savedstate-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ab7f49030ee21d25538e0e6945e4b88b\\transformed\\jetified-kotlinx-coroutines-android-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6db2c170614ff7814a2b897821838d8a\\transformed\\jetified-kotlinx-coroutines-core-jvm-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dc920db8979420613658ec140de7a123\\transformed\\jetified-kotlinx-coroutines-play-services-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\482e7412175e2f7faad2f12bf629cd78\\transformed\\jetified-play-services-tasks-18.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\08ab476b84132df08916644e84a7db98\\transformed\\jetified-firebase-measurement-connector-19.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\54629637e00193b872e853dfaf205b4d\\transformed\\jetified-play-services-basement-18.5.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\deb7f0f4abbc1df04bf2840c7cafbd0c\\transformed\\fragment-1.7.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b08e12880e40b0a05ccc1f0dd89c3ea1\\transformed\\jetified-core-ktx-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7a93e8bfe101c817fed1be9f36bb7f5b\\transformed\\viewpager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0e35ffa2aa874a213429fa4a5748b033\\transformed\\customview-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2b1dfba3187fc43a949ed4cedf3fb3e7\\transformed\\core-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e1cd8d3d001bfb42ac91e3730ff60302\\transformed\\lifecycle-runtime-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9899a1644e2b6fd744e410b34e27a591\\transformed\\jetified-lifecycle-process-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common-java8\\2.7.0\\2ad14aed781c4a73ed4dbb421966d408a0a06686\\lifecycle-common-java8-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common\\2.7.0\\85334205d65cca70ed0109c3acbd29e22a2d9cb1\\lifecycle-common-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5021969e9457d543556a2900b54d7713\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection\\1.1.0\\1f27220b47669781457de0d600849a5de0e89909\\collection-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c19d6aa8f5d8641bc446de295a2ac23d\\transformed\\core-runtime-2.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\06eb1cb0a88080033ef3e31ca975dac4\\transformed\\jetified-savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc070b40f09894a5ca291bcac5bce89d\\transformed\\documentfile-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\71335eb04f03cf589e403916572a7994\\transformed\\localbroadcastmanager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fbb78c32032b9b2265ed47ad4b71d263\\transformed\\print-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7acd876678f2d7d51de5e5a7ccd87e03\\transformed\\jetified-annotation-jvm-1.8.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6fab8d05e38287f76bcc48f3feb0863c\\transformed\\jetified-annotation-experimental-1.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\03af987f05dc7e55bf6d1a427557804d\\transformed\\jetified-kotlin-stdlib-jdk8-1.8.22.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\197860f74432c6bb0e68e9c502b5c894\\transformed\\jetified-kotlin-stdlib-jdk7-1.8.22.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\305b928debbed68a5a926ab9611046a1\\transformed\\jetified-kotlin-stdlib-2.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2d0f4dd6ee9c4f99a5fbe26af0ab6db6\\transformed\\jetified-annotations-23.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a8016a523aac2b3e7fcd2fcfaf91c53a\\transformed\\jetified-guava-31.1-android.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b3168592657e800ce9c86a238db74a65\\transformed\\jetified-listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9d2ac6228c83365ba8747ae5ec8a6af2\\transformed\\jetified-startup-runtime-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\55145276884fc491f4be99cac0578ae5\\transformed\\jetified-tracing-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f79920a910f29c4636ec0a33e3c3fe4c\\transformed\\jetified-failureaccess-1.0.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1fb02798fbf615d64f21603dfe34ff8a\\transformed\\jetified-jsr305-3.0.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\906f14126be04e69d4afe2dcff8cfb7d\\transformed\\jetified-checker-qual-3.12.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\183cd0e29422627a646655d387c18e7c\\transformed\\jetified-error_prone_annotations-2.26.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3466494aa717e474d84da288c9f687a6\\transformed\\jetified-j2objc-annotations-1.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d2f968508d2db1da606bcd7ff75bba4d\\transformed\\jetified-firebase-components-18.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\71d4f853f36e0d595406c7497c2110d4\\transformed\\jetified-firebase-annotations-16.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\41509852b5244efae74c6b62d202e3aa\\transformed\\jetified-javax.inject-1.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b2baf1f54a5165bb17579eb4d0b6eeca\\transformed\\jetified-relinker-1.4.5-api.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\platforms\\android-34\\android.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\build-tools\\33.0.1\\core-lambda-stubs.jar" "-d" "E:\\Ongoing\\lib\\mobile-apps\\hotel-vendor-app\\build\\firebase_analytics\\tmp\\kotlin-classes\\release" "-jvm-target" "17" "-module-name" "firebase_analytics_release" "-no-jdk" "-no-reflect" "-no-stdlib" "E:\\Ongoing\\lib\\mobile-apps\\hotel-vendor-app\\build\\firebase_analytics\\generated\\source\\buildConfig\\release\\io\\flutter\\plugins\\firebase\\analytics\\BuildConfig.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_analytics-11.5.2\\android\\src\\main\\kotlin\\io\\flutter\\plugins\\firebase\\analytics\\Constants.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_analytics-11.5.2\\android\\src\\main\\kotlin\\io\\flutter\\plugins\\firebase\\analytics\\FlutterFirebaseAnalyticsPlugin.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_analytics-11.5.2\\android\\src\\main\\kotlin\\io\\flutter\\plugins\\firebase\\analytics\\FlutterFirebaseAppRegistrar.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_analytics-11.5.2\\android\\src\\main\\kotlin\\io\\flutter\\plugins\\firebase\\analytics\\GeneratedAndroidFirebaseAnalytics.g.kt"