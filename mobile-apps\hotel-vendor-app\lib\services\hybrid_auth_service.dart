import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dynamic_rooms_service.dart';

/// Hybrid authentication service that saves data locally and simulates backend
class HybridAuthService {
  static final HybridAuthService _instance = HybridAuthService._internal();
  factory HybridAuthService() => _instance;
  HybridAuthService._internal();

  // Current user session
  Map<String, dynamic>? _currentUser;

  // Storage keys
  static const String _currentUserKey = 'current_user';
  static const String _usersKey = 'registered_users';
  static const String _hotelsKey = 'user_hotels';
  static const String _bookingsKey = 'user_bookings';

  /// Get current logged-in user
  Map<String, dynamic>? get currentUser => _currentUser;

  /// Check if user is logged in
  bool get isLoggedIn => _currentUser != null;

  /// Initialize the service and restore session
  Future<void> initialize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString(_currentUserKey);
      if (userJson != null) {
        _currentUser = Map<String, dynamic>.from(json.decode(userJson));
      }
    } catch (e) {
      if (kDebugMode) print('Error initializing auth service: $e');
    }
  }

  /// Register a new vendor
  Future<Map<String, dynamic>?> registerVendor({
    required String firstName,
    required String lastName,
    required String email,
    required String phone,
    required String businessName,
    required String password,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Check if user already exists
      final existingUsers = await _getRegisteredUsers();
      if (existingUsers.any((user) => user['email'] == email)) {
        throw Exception('User with this email already exists');
      }

      // Create new user
      final userId = 'user_${DateTime.now().millisecondsSinceEpoch}';
      final newUser = {
        'id': userId,
        'firstName': firstName,
        'lastName': lastName,
        'email': email,
        'password': password, // Store password for authentication
        'phone': phone,
        'businessName': businessName,
        'status': 'verified',
        'createdAt': DateTime.now().toIso8601String(),
        'verifiedAt': DateTime.now().toIso8601String(),
        'hotelIds': <String>[],
        'bankDetails': null,
        ...?additionalData,
      };

      // Save to registered users
      existingUsers.add(newUser);
      await prefs.setString(_usersKey, json.encode(existingUsers));

      if (kDebugMode) {
        print('User registered successfully: ${newUser['email']}');
        print('Total users now: ${existingUsers.length}');
      }

      // Set current user and initialize data
      await _setCurrentUser(newUser);

      // Auto-create a basic hotel for the vendor
      await _createBasicHotel(userId, businessName);

      // Initialize rooms for the new vendor
      final roomsService = DynamicRoomsService();
      await roomsService.initializeNewUserRooms();

      return newUser;
    } catch (e) {
      if (kDebugMode) print('Error registering vendor: $e');
      return null;
    }
  }

  /// Login with email and password
  Future<Map<String, dynamic>?> login(String email, String password) async {
    try {
      final users = await _getRegisteredUsers();
      if (kDebugMode) print('Total registered users: ${users.length}');

      final user = users.firstWhere(
        (u) => u['email'] == email,
        orElse: () => <String, dynamic>{},
      );

      if (user.isEmpty) {
        if (kDebugMode) print('User not found with email: $email');
        // For demo purposes, create a demo user if not found
        if (email == '<EMAIL>' && password == 'demo123') {
          final demoUser = await _createDemoUser();
          return await _setCurrentUser(demoUser);
        }
        throw Exception('User not found with email: $email');
      }

      if (kDebugMode) {
        print('User found: ${user['email']}, checking password...');
      }

      // Check password
      if (user['password'] != password) {
        if (kDebugMode) print('Password mismatch for user: ${user['email']}');
        throw Exception('Invalid password');
      }

      if (kDebugMode) print('Login successful for user: ${user['email']}');
      return await _setCurrentUser(user);
    } catch (e) {
      if (kDebugMode) print('Error logging in: $e');
      return null;
    }
  }

  /// Logout current user
  Future<void> logout() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_currentUserKey);
      _currentUser = null;
    } catch (e) {
      if (kDebugMode) print('Error logging out: $e');
    }
  }

  /// Update vendor profile
  Future<bool> updateVendorProfile(Map<String, dynamic> updates) async {
    try {
      if (_currentUser == null) return false;

      final prefs = await SharedPreferences.getInstance();
      final users = await _getRegisteredUsers();

      final userIndex = users.indexWhere((u) => u['id'] == _currentUser!['id']);
      if (userIndex == -1) return false;

      // Update user data
      users[userIndex] = {
        ...users[userIndex],
        ...updates,
        'updatedAt': DateTime.now().toIso8601String()
      };
      await prefs.setString(_usersKey, json.encode(users));

      // Update current user session
      _currentUser = users[userIndex];
      await prefs.setString(_currentUserKey, json.encode(_currentUser));

      return true;
    } catch (e) {
      if (kDebugMode) print('Error updating profile: $e');
      return false;
    }
  }

  /// Get user's hotels
  Future<List<Map<String, dynamic>>> getUserHotels(String userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final hotelsJson = prefs.getString('${_hotelsKey}_$userId');
      if (hotelsJson != null) {
        final List<dynamic> hotelsList = json.decode(hotelsJson);
        return hotelsList.cast<Map<String, dynamic>>();
      }
      return [];
    } catch (e) {
      if (kDebugMode) print('Error getting user hotels: $e');
      return [];
    }
  }

  /// Add hotel for user
  Future<bool> addHotel(String userId, Map<String, dynamic> hotel) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final hotels = await getUserHotels(userId);

      final hotelId = 'hotel_${DateTime.now().millisecondsSinceEpoch}';
      final newHotel = {
        ...hotel,
        'id': hotelId,
        'vendorId': userId,
        'status': 'approved',
        'createdAt': DateTime.now().toIso8601String(),
      };

      hotels.add(newHotel);
      await prefs.setString('${_hotelsKey}_$userId', json.encode(hotels));

      // Update user's hotel IDs
      if (_currentUser != null && _currentUser!['id'] == userId) {
        final hotelIds = List<String>.from(_currentUser!['hotelIds'] ?? []);
        hotelIds.add(hotelId);
        await updateVendorProfile({'hotelIds': hotelIds});
      }

      return true;
    } catch (e) {
      if (kDebugMode) print('Error adding hotel: $e');
      return false;
    }
  }

  /// Get user's bookings
  Future<List<Map<String, dynamic>>> getUserBookings(String userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final bookingsJson = prefs.getString('${_bookingsKey}_$userId');
      if (bookingsJson != null) {
        final List<dynamic> bookingsList = json.decode(bookingsJson);
        return bookingsList.cast<Map<String, dynamic>>();
      }

      // Return demo bookings for new users
      return _createDemoBookings(userId);
    } catch (e) {
      if (kDebugMode) print('Error getting user bookings: $e');
      return [];
    }
  }

  /// Private helper methods
  Future<List<Map<String, dynamic>>> _getRegisteredUsers() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final usersJson = prefs.getString(_usersKey);
      if (usersJson != null) {
        final List<dynamic> usersList = json.decode(usersJson);
        return usersList.cast<Map<String, dynamic>>();
      }
      return [];
    } catch (e) {
      return [];
    }
  }

  Future<Map<String, dynamic>> _setCurrentUser(
      Map<String, dynamic> user) async {
    _currentUser = user;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_currentUserKey, json.encode(user));
    return user;
  }

  Future<Map<String, dynamic>> _createDemoUser() async {
    final demoUser = {
      'id': 'demo_user_001',
      'firstName': 'Demo',
      'lastName': 'Vendor',
      'email': '<EMAIL>',
      'password': 'demo123', // Add password for demo user
      'phone': '+91 **********',
      'businessName': 'Demo Hotels Pvt Ltd',
      'status': 'verified',
      'createdAt':
          DateTime.now().subtract(const Duration(days: 30)).toIso8601String(),
      'verifiedAt':
          DateTime.now().subtract(const Duration(days: 25)).toIso8601String(),
      'hotelIds': ['demo_hotel_001'],
      'bankDetails': {
        'accountHolderName': 'Demo Hotels Pvt Ltd',
        'accountNumber': '****************',
        'ifscCode': 'HDFC0001234',
        'bankName': 'HDFC Bank',
        'branchName': 'Mumbai Main Branch',
      },
    };

    // Save demo user
    final users = await _getRegisteredUsers();
    if (!users.any((u) => u['id'] == demoUser['id'])) {
      users.add(demoUser);
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_usersKey, json.encode(users));
    }

    // Create demo hotel
    await _createBasicHotel(
        demoUser['id'] as String, demoUser['businessName'] as String);

    return demoUser;
  }

  Future<void> _createBasicHotel(String userId, String businessName) async {
    final basicHotel = {
      'hotelName': '$businessName Hotel',
      'description':
          'Welcome to our hotel! Please update this description with your hotel details.',
      'address': {
        'street': '123 Business Street',
        'city': 'Mumbai',
        'state': 'Maharashtra',
        'pincode': '400001',
        'country': 'India',
      },
      'amenities': ['WiFi', 'Parking', 'Room Service'],
      'hotelType': 'hotel',
      'starRating': 3,
      'contactInfo': {
        'email': _currentUser?['email'] ?? '<EMAIL>',
        'phone': _currentUser?['phone'] ?? '+91 **********',
      },
      'roomTypes': [
        {
          'name': 'Standard Room',
          'description': 'Comfortable standard room with basic amenities',
          'maxOccupancy': 2,
          'basePrice': 2000.0,
          'amenities': ['WiFi', 'Air Conditioning', 'TV'],
          'totalRooms': 10,
        },
      ],
    };

    await addHotel(userId, basicHotel);
  }

  List<Map<String, dynamic>> _createDemoBookings(String userId) {
    return [
      {
        'id': 'booking_001_$userId',
        'vendorId': userId,
        'hotelId': 'hotel_001_$userId',
        'customerName': 'John Smith',
        'customerEmail': '<EMAIL>',
        'customerPhone': '+91 9876543210',
        'roomNumber': '101',
        'roomType': 'Deluxe Room',
        'checkInDate':
            DateTime.now().subtract(const Duration(days: 2)).toIso8601String(),
        'checkOutDate':
            DateTime.now().add(const Duration(days: 1)).toIso8601String(),
        'numberOfGuests': 2,
        'status': 'checked_in',
        'aadharVerified': true,
        'aadharNumber': '1234-5678-9012',
        'totalAmount': 4500.0,
        'specialRequests': 'Late checkout requested',
        'bookingDate':
            DateTime.now().subtract(const Duration(days: 5)).toIso8601String(),
      },
      {
        'id': 'booking_002_$userId',
        'vendorId': userId,
        'hotelId': 'hotel_001_$userId',
        'customerName': 'Sarah Johnson',
        'customerEmail': '<EMAIL>',
        'customerPhone': '+91 8765432109',
        'roomNumber': '205',
        'roomType': 'Suite',
        'checkInDate':
            DateTime.now().subtract(const Duration(days: 1)).toIso8601String(),
        'checkOutDate':
            DateTime.now().add(const Duration(days: 2)).toIso8601String(),
        'numberOfGuests': 1,
        'status': 'checked_in',
        'aadharVerified': false,
        'aadharNumber': null,
        'totalAmount': 7500.0,
        'specialRequests': 'Extra pillows',
        'bookingDate':
            DateTime.now().subtract(const Duration(days: 3)).toIso8601String(),
      },
    ];
  }
}
