// Mock services for development when Firebase is not configured
class MockHotelRegistrationService {
  static final MockHotelRegistrationService _instance =
      MockHotelRegistrationService._internal();
  factory MockHotelRegistrationService() => _instance;
  MockHotelRegistrationService._internal();

  // Mock data
  static final List<Map<String, dynamic>> _mockBookings = [
    {
      'id': 'booking_001',
      'vendorId': 'vendor_001',
      'hotelId': 'hotel_001',
      'customerName': '<PERSON>',
      'customerEmail': '<EMAIL>',
      'customerPhone': '+91 9876543210',
      'roomNumber': '101',
      'roomType': 'Deluxe Room',
      'checkInDate': DateTime.now().subtract(const Duration(days: 2)),
      'checkOutDate': DateTime.now().add(const Duration(days: 1)),
      'numberOfGuests': 2,
      'status': 'checked_in',
      'aadharVerified': true,
      'aadharNumber': '1234-5678-9012',
      'totalAmount': 4500.0,
      'specialRequests': 'Late checkout requested',
      'bookingDate': DateTime.now().subtract(const Duration(days: 5)),
    },
    {
      'id': 'booking_002',
      'vendorId': 'vendor_001',
      'hotelId': 'hotel_001',
      'customerName': 'Sarah Johnson',
      'customerEmail': '<EMAIL>',
      'customerPhone': '+91 8765432109',
      'roomNumber': '205',
      'roomType': 'Suite',
      'checkInDate': DateTime.now().subtract(const Duration(days: 1)),
      'checkOutDate': DateTime.now().add(const Duration(days: 2)),
      'numberOfGuests': 1,
      'status': 'checked_in',
      'aadharVerified': false,
      'aadharNumber': null,
      'totalAmount': 7500.0,
      'specialRequests': 'Extra pillows',
      'bookingDate': DateTime.now().subtract(const Duration(days: 3)),
    },
    {
      'id': 'booking_003',
      'vendorId': 'vendor_001',
      'hotelId': 'hotel_001',
      'customerName': 'Mike Wilson',
      'customerEmail': '<EMAIL>',
      'customerPhone': '+91 7654321098',
      'roomNumber': '302',
      'roomType': 'Standard Room',
      'checkInDate': DateTime.now(),
      'checkOutDate': DateTime.now().add(const Duration(days: 3)),
      'numberOfGuests': 2,
      'status': 'confirmed',
      'aadharVerified': false,
      'aadharNumber': null,
      'totalAmount': 3600.0,
      'specialRequests': 'Ground floor preferred',
      'bookingDate': DateTime.now().subtract(const Duration(days: 1)),
    },
    {
      'id': 'booking_004',
      'vendorId': 'vendor_001',
      'hotelId': 'hotel_001',
      'customerName': 'Emily Davis',
      'customerEmail': '<EMAIL>',
      'customerPhone': '+91 6543210987',
      'roomNumber': '150',
      'roomType': 'Deluxe Room',
      'checkInDate': DateTime.now().subtract(const Duration(days: 3)),
      'checkOutDate': DateTime.now(),
      'numberOfGuests': 1,
      'status': 'checked_in',
      'aadharVerified': true,
      'aadharNumber': '**************',
      'totalAmount': 4200.0,
      'specialRequests': 'Early breakfast',
      'bookingDate': DateTime.now().subtract(const Duration(days: 7)),
    },
  ];

  static final Map<String, dynamic> _mockVendor = {
    'id': 'vendor_001',
    'firstName': 'Hotel',
    'lastName': 'Owner',
    'email': '<EMAIL>',
    'phone': '+91 9999999999',
    'businessName': 'Premium Hotels Pvt Ltd',
    'businessRegistrationNumber': 'REG123456789',
    'panNumber': '**********',
    'gstNumber': '22AAAAA0000A1Z5',
    'address': {
      'street': '123 Business Street',
      'city': 'Mumbai',
      'state': 'Maharashtra',
      'pincode': '400001',
      'country': 'India',
    },
    'hotelIds': ['hotel_001'],
    'status': 'verified',
    'createdAt': DateTime.now().subtract(const Duration(days: 30)),
    'verifiedAt': DateTime.now().subtract(const Duration(days: 25)),
    'bankDetails': {
      'accountHolderName': 'Premium Hotels Pvt Ltd',
      'accountNumber': '****************',
      'ifscCode': 'HDFC0001234',
      'bankName': 'HDFC Bank',
      'branchName': 'Mumbai Main Branch',
    },
  };

  // Mock methods
  Future<List<Map<String, dynamic>>> getBookingsByStatus(
      String vendorId, String status) async {
    await Future.delayed(
        const Duration(milliseconds: 500)); // Simulate network delay

    if (status == 'all') {
      return _mockBookings;
    }

    return _mockBookings
        .where((booking) => booking['status'] == status)
        .toList();
  }

  Future<List<Map<String, dynamic>>> getTodayCheckIns(String vendorId) async {
    await Future.delayed(const Duration(milliseconds: 500));

    final today = DateTime.now();
    return _mockBookings.where((booking) {
      final checkInDate = booking['checkInDate'] as DateTime;
      return checkInDate.year == today.year &&
          checkInDate.month == today.month &&
          checkInDate.day == today.day &&
          booking['status'] == 'confirmed';
    }).toList();
  }

  Future<List<Map<String, dynamic>>> getTodayCheckOuts(String vendorId) async {
    await Future.delayed(const Duration(milliseconds: 500));

    final today = DateTime.now();
    return _mockBookings.where((booking) {
      final checkOutDate = booking['checkOutDate'] as DateTime;
      return checkOutDate.year == today.year &&
          checkOutDate.month == today.month &&
          checkOutDate.day == today.day &&
          booking['status'] == 'checked_in';
    }).toList();
  }

  Future<List<Map<String, dynamic>>> getCurrentGuests(String vendorId) async {
    await Future.delayed(const Duration(milliseconds: 500));

    return _mockBookings
        .where((booking) => booking['status'] == 'checked_in')
        .toList();
  }

  Future<bool> updateBookingStatus(String bookingId, String status) async {
    await Future.delayed(const Duration(milliseconds: 300));

    final bookingIndex =
        _mockBookings.indexWhere((booking) => booking['id'] == bookingId);
    if (bookingIndex != -1) {
      _mockBookings[bookingIndex]['status'] = status;
      return true;
    }
    return false;
  }

  Future<bool> checkInGuest(String bookingId, {String? aadharNumber}) async {
    await Future.delayed(const Duration(milliseconds: 300));

    final bookingIndex =
        _mockBookings.indexWhere((booking) => booking['id'] == bookingId);
    if (bookingIndex != -1) {
      _mockBookings[bookingIndex]['status'] = 'checked_in';
      if (aadharNumber != null) {
        _mockBookings[bookingIndex]['aadharNumber'] = aadharNumber;
        _mockBookings[bookingIndex]['aadharVerified'] = true;
      }
      return true;
    }
    return false;
  }

  Future<bool> checkOutGuest(String bookingId) async {
    await Future.delayed(const Duration(milliseconds: 300));

    final bookingIndex =
        _mockBookings.indexWhere((booking) => booking['id'] == bookingId);
    if (bookingIndex != -1) {
      _mockBookings[bookingIndex]['status'] = 'checked_out';
      return true;
    }
    return false;
  }

  Future<bool> verifyAadhar(String bookingId, String aadharNumber) async {
    await Future.delayed(const Duration(milliseconds: 300));

    final bookingIndex =
        _mockBookings.indexWhere((booking) => booking['id'] == bookingId);
    if (bookingIndex != -1) {
      _mockBookings[bookingIndex]['aadharNumber'] = aadharNumber;
      _mockBookings[bookingIndex]['aadharVerified'] = true;
      return true;
    }
    return false;
  }

  Future<List<Map<String, dynamic>>> searchBookings(
      String vendorId, String searchTerm) async {
    await Future.delayed(const Duration(milliseconds: 500));

    final searchLower = searchTerm.toLowerCase();
    return _mockBookings.where((booking) {
      final customerName =
          (booking['customerName'] ?? '').toString().toLowerCase();
      final customerPhone =
          (booking['customerPhone'] ?? '').toString().toLowerCase();
      final roomNumber = (booking['roomNumber'] ?? '').toString().toLowerCase();

      return customerName.contains(searchLower) ||
          customerPhone.contains(searchLower) ||
          roomNumber.contains(searchLower);
    }).toList();
  }

  Future<Map<String, dynamic>> getBookingAnalytics(String vendorId) async {
    await Future.delayed(const Duration(milliseconds: 500));

    final totalBookings = _mockBookings.length;
    final totalRevenue = _mockBookings.fold<double>(
        0.0, (sum, booking) => sum + (booking['totalAmount'] ?? 0.0));
    final monthlyBookings = _mockBookings.where((booking) {
      final bookingDate = booking['bookingDate'] as DateTime;
      final now = DateTime.now();
      return bookingDate.year == now.year && bookingDate.month == now.month;
    }).length;

    final monthlyRevenue = _mockBookings.where((booking) {
      final bookingDate = booking['bookingDate'] as DateTime;
      final now = DateTime.now();
      return bookingDate.year == now.year && bookingDate.month == now.month;
    }).fold<double>(
        0.0, (sum, booking) => sum + (booking['totalAmount'] ?? 0.0));

    return {
      'monthlyBookings': monthlyBookings,
      'totalBookings': totalBookings,
      'monthlyRevenue': monthlyRevenue,
      'totalRevenue': totalRevenue,
      'averageBookingValue':
          totalBookings > 0 ? totalRevenue / totalBookings : 0.0,
    };
  }

  Map<String, dynamic> getMockVendor() {
    return Map<String, dynamic>.from(_mockVendor);
  }

  Future<Map<String, dynamic>> getRoomAvailability(String hotelId) async {
    await Future.delayed(const Duration(milliseconds: 300));

    return {
      'totalRooms': 50,
      'occupiedRooms': 32,
      'availableRooms': 18,
      'occupancyRate': 64.0,
    };
  }
}

class MockVendorAuthService {
  static final MockVendorAuthService _instance =
      MockVendorAuthService._internal();
  factory MockVendorAuthService() => _instance;
  MockVendorAuthService._internal();

  Map<String, dynamic>? get currentVendor =>
      MockHotelRegistrationService().getMockVendor();

  Future<bool> updateVendorProfile(Map<String, dynamic> updates) async {
    await Future.delayed(const Duration(milliseconds: 300));
    // In a real implementation, this would update the vendor data
    return true;
  }

  Future<void> updatePassword(String newPassword) async {
    await Future.delayed(const Duration(milliseconds: 300));
    // Mock password update
  }

  Future<void> reauthenticate(String password) async {
    await Future.delayed(const Duration(milliseconds: 300));
    // Mock reauthentication
  }

  Future<void> signOut() async {
    await Future.delayed(const Duration(milliseconds: 300));
    // Mock sign out
  }

  Future<bool> deleteAccount() async {
    await Future.delayed(const Duration(milliseconds: 300));
    // Mock account deletion
    return true;
  }
}

class MockHotelVendorProvider {
  final MockHotelRegistrationService _service = MockHotelRegistrationService();

  List<Map<String, dynamic>> get vendorHotels => [
        {
          'id': 'hotel_001',
          'hotelName': 'Premium Hotel Mumbai',
          'status': 'approved',
        }
      ];

  int get approvedHotelsCount => 1;
  int get totalBookings => 156;
  double get totalRevenue => 245000.0;
  double get monthlyRevenue => 45000.0;

  Future<bool> updateBankDetails(Map<String, dynamic> bankDetails) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return true;
  }
}
