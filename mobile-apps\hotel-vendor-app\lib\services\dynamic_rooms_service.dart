import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'hybrid_auth_service.dart';

/// Dynamic rooms service that manages all user-specific room data
class DynamicRoomsService {
  static final DynamicRoomsService _instance = DynamicRoomsService._internal();
  factory DynamicRoomsService() => _instance;
  DynamicRoomsService._internal();

  final _authService = HybridAuthService();

  // Storage keys
  static const String _roomsPrefix = 'user_rooms_';
  static const String _roomTypesPrefix = 'user_room_types_';

  /// Get current user ID
  String? get _currentUserId => _authService.currentUser?['id'];

  /// Get user's rooms
  Future<List<Map<String, dynamic>>> getUserRooms() async {
    if (_currentUserId == null) return [];
    
    try {
      final prefs = await SharedPreferences.getInstance();
      final roomsJson = prefs.getString('$_roomsPrefix$_currentUserId');
      if (roomsJson != null) {
        final List<dynamic> roomsList = json.decode(roomsJson);
        return roomsList.cast<Map<String, dynamic>>();
      }
      return [];
    } catch (e) {
      if (kDebugMode) print('Error getting user rooms: $e');
      return [];
    }
  }

  /// Add room for current user
  Future<bool> addRoom(Map<String, dynamic> roomData) async {
    if (_currentUserId == null) return false;

    try {
      final rooms = await getUserRooms();
      final roomId = 'room_${DateTime.now().millisecondsSinceEpoch}';
      
      final newRoom = {
        ...roomData,
        'id': roomId,
        'vendorId': _currentUserId,
        'status': roomData['status'] ?? 'Available',
        'createdAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
      };

      rooms.add(newRoom);
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('$_roomsPrefix$_currentUserId', json.encode(rooms));
      
      return true;
    } catch (e) {
      if (kDebugMode) print('Error adding room: $e');
      return false;
    }
  }

  /// Update room
  Future<bool> updateRoom(String roomId, Map<String, dynamic> updates) async {
    if (_currentUserId == null) return false;

    try {
      final rooms = await getUserRooms();
      final roomIndex = rooms.indexWhere((room) => room['id'] == roomId);
      
      if (roomIndex == -1) return false;

      rooms[roomIndex] = {
        ...rooms[roomIndex],
        ...updates,
        'updatedAt': DateTime.now().toIso8601String(),
      };

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('$_roomsPrefix$_currentUserId', json.encode(rooms));
      
      return true;
    } catch (e) {
      if (kDebugMode) print('Error updating room: $e');
      return false;
    }
  }

  /// Delete room
  Future<bool> deleteRoom(String roomId) async {
    if (_currentUserId == null) return false;

    try {
      final rooms = await getUserRooms();
      rooms.removeWhere((room) => room['id'] == roomId);

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('$_roomsPrefix$_currentUserId', json.encode(rooms));
      
      return true;
    } catch (e) {
      if (kDebugMode) print('Error deleting room: $e');
      return false;
    }
  }

  /// Get room by ID
  Future<Map<String, dynamic>?> getRoomById(String roomId) async {
    final rooms = await getUserRooms();
    try {
      return rooms.firstWhere((room) => room['id'] == roomId);
    } catch (e) {
      return null;
    }
  }

  /// Get rooms by status
  Future<List<Map<String, dynamic>>> getRoomsByStatus(String status) async {
    final rooms = await getUserRooms();
    if (status == 'all') return rooms;
    return rooms.where((room) => room['status']?.toLowerCase() == status.toLowerCase()).toList();
  }

  /// Update room status
  Future<bool> updateRoomStatus(String roomId, String status) async {
    return await updateRoom(roomId, {'status': status});
  }

  /// Get room statistics
  Future<Map<String, dynamic>> getRoomStatistics() async {
    final rooms = await getUserRooms();
    
    final available = rooms.where((r) => r['status']?.toLowerCase() == 'available').length;
    final occupied = rooms.where((r) => r['status']?.toLowerCase() == 'occupied').length;
    final maintenance = rooms.where((r) => r['status']?.toLowerCase() == 'maintenance').length;
    final cleaning = rooms.where((r) => r['status']?.toLowerCase() == 'cleaning').length;
    
    return {
      'total': rooms.length,
      'available': available,
      'occupied': occupied,
      'maintenance': maintenance,
      'cleaning': cleaning,
      'occupancyRate': rooms.isNotEmpty ? (occupied / rooms.length) * 100 : 0.0,
    };
  }

  /// Search rooms
  Future<List<Map<String, dynamic>>> searchRooms(String searchTerm) async {
    final rooms = await getUserRooms();
    final searchLower = searchTerm.toLowerCase();
    
    return rooms.where((room) {
      final roomNumber = (room['roomNumber'] ?? room['id'] ?? '').toString().toLowerCase();
      final roomType = (room['type'] ?? '').toString().toLowerCase();
      final status = (room['status'] ?? '').toString().toLowerCase();
      
      return roomNumber.contains(searchLower) ||
             roomType.contains(searchLower) ||
             status.contains(searchLower);
    }).toList();
  }

  /// Initialize user with sample rooms (only for new users)
  Future<void> initializeNewUserRooms() async {
    if (_currentUserId == null) return;

    final existingRooms = await getUserRooms();
    if (existingRooms.isNotEmpty) return; // User already has rooms

    // Add some initial rooms for demonstration
    final sampleRooms = [
      {
        'roomNumber': '101',
        'type': 'Deluxe Room',
        'status': 'Available',
        'price': 5000.0,
        'capacity': 2,
        'amenities': ['WiFi', 'AC', 'TV', 'Mini Bar'],
        'description': 'Spacious deluxe room with modern amenities',
        'floor': 1,
        'bedType': 'King Size',
        'image': 'https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=400&h=300&fit=crop',
      },
      {
        'roomNumber': '102',
        'type': 'Standard Room',
        'status': 'Occupied',
        'price': 3500.0,
        'capacity': 2,
        'amenities': ['WiFi', 'AC', 'TV'],
        'description': 'Comfortable standard room with essential amenities',
        'floor': 1,
        'bedType': 'Queen Size',
        'image': 'https://images.unsplash.com/photo-1522771739844-6a9f6d5f14af?w=400&h=300&fit=crop',
      },
      {
        'roomNumber': '201',
        'type': 'Suite',
        'status': 'Available',
        'price': 8000.0,
        'capacity': 4,
        'amenities': ['WiFi', 'AC', 'TV', 'Mini Bar', 'Balcony', 'Jacuzzi'],
        'description': 'Luxurious suite with premium amenities and city view',
        'floor': 2,
        'bedType': 'King Size',
        'image': 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',
      },
      {
        'roomNumber': '202',
        'type': 'Executive Room',
        'status': 'Maintenance',
        'price': 6000.0,
        'capacity': 2,
        'amenities': ['WiFi', 'AC', 'TV', 'Mini Bar', 'Work Desk'],
        'description': 'Executive room perfect for business travelers',
        'floor': 2,
        'bedType': 'King Size',
        'image': 'https://images.unsplash.com/photo-1618773928121-c32242e63f39?w=400&h=300&fit=crop',
      },
    ];

    for (final room in sampleRooms) {
      await addRoom(room);
    }
  }

  /// Get available room types
  List<String> getAvailableRoomTypes() {
    return [
      'Standard Room',
      'Deluxe Room',
      'Suite',
      'Executive Room',
      'Presidential Suite',
      'Family Room',
      'Twin Room',
      'Single Room',
      'Double Room',
      'Triple Room',
    ];
  }

  /// Get available amenities
  List<String> getAvailableAmenities() {
    return [
      'WiFi',
      'AC',
      'TV',
      'Mini Bar',
      'Room Service',
      'Balcony',
      'Sea View',
      'City View',
      'Bathtub',
      'Shower',
      'Safe',
      'Telephone',
      'Hair Dryer',
      'Iron',
      'Coffee Maker',
      'Refrigerator',
      'Work Desk',
      'Jacuzzi',
      'Kitchenette',
    ];
  }

  /// Get room status options
  List<String> getRoomStatusOptions() {
    return [
      'Available',
      'Occupied',
      'Maintenance',
      'Cleaning',
      'Reserved',
      'Out of Order',
    ];
  }

  /// Clear all user room data (for logout)
  Future<void> clearUserRoomData() async {
    if (_currentUserId == null) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('$_roomsPrefix$_currentUserId');
      await prefs.remove('$_roomTypesPrefix$_currentUserId');
    } catch (e) {
      if (kDebugMode) print('Error clearing user room data: $e');
    }
  }
}
