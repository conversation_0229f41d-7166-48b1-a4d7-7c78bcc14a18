[{"merged": "com.baseflow.geolocator.geolocator_android-merged_res-27:/drawable-ldpi-v4/ic_call_answer_video_low.png", "source": "com.baseflow.geolocator.geolocator_android-core-1.13.1-11:/drawable-ldpi-v4/ic_call_answer_video_low.png"}, {"merged": "com.baseflow.geolocator.geolocator_android-merged_res-27:/drawable-ldpi-v4/ic_call_decline.png", "source": "com.baseflow.geolocator.geolocator_android-core-1.13.1-11:/drawable-ldpi-v4/ic_call_decline.png"}, {"merged": "com.baseflow.geolocator.geolocator_android-merged_res-27:/drawable-ldpi-v4/ic_call_answer_low.png", "source": "com.baseflow.geolocator.geolocator_android-core-1.13.1-11:/drawable-ldpi-v4/ic_call_answer_low.png"}, {"merged": "com.baseflow.geolocator.geolocator_android-merged_res-27:/drawable-ldpi-v4/ic_call_answer_video.png", "source": "com.baseflow.geolocator.geolocator_android-core-1.13.1-11:/drawable-ldpi-v4/ic_call_answer_video.png"}, {"merged": "com.baseflow.geolocator.geolocator_android-merged_res-27:/drawable-ldpi-v4/ic_call_answer.png", "source": "com.baseflow.geolocator.geolocator_android-core-1.13.1-11:/drawable-ldpi-v4/ic_call_answer.png"}, {"merged": "com.baseflow.geolocator.geolocator_android-merged_res-27:/drawable-ldpi-v4/ic_call_decline_low.png", "source": "com.baseflow.geolocator.geolocator_android-core-1.13.1-11:/drawable-ldpi-v4/ic_call_decline_low.png"}]