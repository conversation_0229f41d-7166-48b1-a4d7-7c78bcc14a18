import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'hybrid_auth_service.dart';

/// Firebase Firestore service for bookings management
class FirebaseBookingsService {
  static final FirebaseBookingsService _instance = FirebaseBookingsService._internal();
  factory FirebaseBookingsService() => _instance;
  FirebaseBookingsService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final HybridAuthService _authService = HybridAuthService();

  /// Get current user ID
  String? get _currentUserId => _authService.currentUser?['id'];

  /// Get user's bookings collection reference
  CollectionReference get _bookingsCollection {
    if (_currentUserId == null) {
      throw Exception('User not authenticated');
    }
    return _firestore.collection('vendors').doc(_currentUserId).collection('bookings');
  }

  /// Get all bookings for current user
  Future<List<Map<String, dynamic>>> getUserBookings() async {
    try {
      if (_currentUserId == null) return [];

      final querySnapshot = await _bookingsCollection.orderBy('createdAt', descending: true).get();
      return querySnapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      if (kDebugMode) print('Error getting user bookings: $e');
      return [];
    }
  }

  /// Get bookings by status
  Future<List<Map<String, dynamic>>> getBookingsByStatus(String status) async {
    try {
      if (_currentUserId == null) return [];

      Query query = _bookingsCollection;
      if (status != 'all') {
        query = query.where('status', isEqualTo: status);
      }

      final querySnapshot = await query.orderBy('createdAt', descending: true).get();
      return querySnapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      if (kDebugMode) print('Error getting bookings by status: $e');
      return [];
    }
  }

  /// Add new booking
  Future<bool> addBooking(Map<String, dynamic> bookingData) async {
    try {
      if (_currentUserId == null) return false;

      final newBookingData = {
        ...bookingData,
        'vendorId': _currentUserId,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      };

      await _bookingsCollection.add(newBookingData);
      
      if (kDebugMode) print('Booking added successfully to Firestore');
      return true;
    } catch (e) {
      if (kDebugMode) print('Error adding booking to Firestore: $e');
      return false;
    }
  }

  /// Update booking
  Future<bool> updateBooking(String bookingId, Map<String, dynamic> updates) async {
    try {
      if (_currentUserId == null) return false;

      final updateData = {
        ...updates,
        'updatedAt': FieldValue.serverTimestamp(),
      };

      await _bookingsCollection.doc(bookingId).update(updateData);
      
      if (kDebugMode) print('Booking updated successfully in Firestore');
      return true;
    } catch (e) {
      if (kDebugMode) print('Error updating booking in Firestore: $e');
      return false;
    }
  }

  /// Delete booking
  Future<bool> deleteBooking(String bookingId) async {
    try {
      if (_currentUserId == null) return false;

      await _bookingsCollection.doc(bookingId).delete();
      
      if (kDebugMode) print('Booking deleted successfully from Firestore');
      return true;
    } catch (e) {
      if (kDebugMode) print('Error deleting booking from Firestore: $e');
      return false;
    }
  }

  /// Get booking by ID
  Future<Map<String, dynamic>?> getBookingById(String bookingId) async {
    try {
      if (_currentUserId == null) return null;

      final doc = await _bookingsCollection.doc(bookingId).get();
      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return data;
      }
      return null;
    } catch (e) {
      if (kDebugMode) print('Error getting booking by ID: $e');
      return null;
    }
  }

  /// Update booking status
  Future<bool> updateBookingStatus(String bookingId, String status) async {
    return await updateBooking(bookingId, {'status': status});
  }

  /// Get booking statistics
  Future<Map<String, dynamic>> getBookingStatistics() async {
    try {
      if (_currentUserId == null) {
        return {
          'total': 0,
          'confirmed': 0,
          'checkedIn': 0,
          'checkedOut': 0,
          'cancelled': 0,
          'pending': 0,
          'totalRevenue': 0.0,
          'monthlyRevenue': 0.0,
        };
      }

      final bookings = await getUserBookings();
      
      final confirmed = bookings.where((b) => b['status']?.toLowerCase() == 'confirmed').length;
      final checkedIn = bookings.where((b) => b['status']?.toLowerCase() == 'checked-in').length;
      final checkedOut = bookings.where((b) => b['status']?.toLowerCase() == 'checked-out').length;
      final cancelled = bookings.where((b) => b['status']?.toLowerCase() == 'cancelled').length;
      final pending = bookings.where((b) => b['status']?.toLowerCase() == 'pending').length;
      
      // Calculate revenue
      final totalRevenue = bookings.fold<double>(0.0, (sum, booking) {
        final amount = booking['totalAmount'] ?? 0.0;
        return sum + (amount is num ? amount.toDouble() : 0.0);
      });

      // Calculate monthly revenue (current month)
      final now = DateTime.now();
      final currentMonth = DateTime(now.year, now.month);
      final monthlyBookings = bookings.where((booking) {
        final createdAt = booking['createdAt'];
        if (createdAt is Timestamp) {
          final bookingDate = createdAt.toDate();
          return bookingDate.isAfter(currentMonth);
        }
        return false;
      });

      final monthlyRevenue = monthlyBookings.fold<double>(0.0, (sum, booking) {
        final amount = booking['totalAmount'] ?? 0.0;
        return sum + (amount is num ? amount.toDouble() : 0.0);
      });
      
      return {
        'total': bookings.length,
        'confirmed': confirmed,
        'checkedIn': checkedIn,
        'checkedOut': checkedOut,
        'cancelled': cancelled,
        'pending': pending,
        'totalRevenue': totalRevenue,
        'monthlyRevenue': monthlyRevenue,
      };
    } catch (e) {
      if (kDebugMode) print('Error getting booking statistics: $e');
      return {
        'total': 0,
        'confirmed': 0,
        'checkedIn': 0,
        'checkedOut': 0,
        'cancelled': 0,
        'pending': 0,
        'totalRevenue': 0.0,
        'monthlyRevenue': 0.0,
      };
    }
  }

  /// Search bookings
  Future<List<Map<String, dynamic>>> searchBookings(String searchTerm) async {
    try {
      if (_currentUserId == null) return [];

      // Firestore doesn't support full-text search, so we'll get all bookings and filter locally
      final bookings = await getUserBookings();
      final searchLower = searchTerm.toLowerCase();
      
      return bookings.where((booking) {
        final bookingId = (booking['id'] ?? '').toString().toLowerCase();
        final guestName = (booking['guestName'] ?? '').toString().toLowerCase();
        final guestEmail = (booking['guestEmail'] ?? '').toString().toLowerCase();
        final roomNumber = (booking['roomNumber'] ?? '').toString().toLowerCase();
        final status = (booking['status'] ?? '').toString().toLowerCase();
        
        return bookingId.contains(searchLower) ||
               guestName.contains(searchLower) ||
               guestEmail.contains(searchLower) ||
               roomNumber.contains(searchLower) ||
               status.contains(searchLower);
      }).toList();
    } catch (e) {
      if (kDebugMode) print('Error searching bookings: $e');
      return [];
    }
  }

  /// Initialize user with sample bookings (only for new users)
  Future<void> initializeNewUserBookings() async {
    try {
      if (_currentUserId == null) return;

      final existingBookings = await getUserBookings();
      if (existingBookings.isNotEmpty) return; // User already has bookings

      if (kDebugMode) print('Initializing sample bookings in Firestore for new user');

      final now = DateTime.now();
      
      // Add some initial bookings for demonstration
      final sampleBookings = [
        {
          'guestName': 'John Doe',
          'guestEmail': '<EMAIL>',
          'guestPhone': '+91 9876543210',
          'roomNumber': '101',
          'roomType': 'Deluxe Room',
          'checkInDate': Timestamp.fromDate(now.add(const Duration(days: 1))),
          'checkOutDate': Timestamp.fromDate(now.add(const Duration(days: 3))),
          'numberOfGuests': 2,
          'numberOfNights': 2,
          'roomRate': 5000.0,
          'totalAmount': 10000.0,
          'status': 'Confirmed',
          'paymentStatus': 'Paid',
          'specialRequests': 'Late check-in requested',
          'aadharVerified': false,
        },
        {
          'guestName': 'Jane Smith',
          'guestEmail': '<EMAIL>',
          'guestPhone': '+91 9876543211',
          'roomNumber': '102',
          'roomType': 'Standard Room',
          'checkInDate': Timestamp.fromDate(now.subtract(const Duration(days: 1))),
          'checkOutDate': Timestamp.fromDate(now.add(const Duration(days: 1))),
          'numberOfGuests': 1,
          'numberOfNights': 2,
          'roomRate': 3500.0,
          'totalAmount': 7000.0,
          'status': 'Checked-In',
          'paymentStatus': 'Paid',
          'specialRequests': '',
          'aadharVerified': true,
        },
        {
          'guestName': 'Mike Johnson',
          'guestEmail': '<EMAIL>',
          'guestPhone': '+91 9876543212',
          'roomNumber': '201',
          'roomType': 'Suite',
          'checkInDate': Timestamp.fromDate(now.add(const Duration(days: 5))),
          'checkOutDate': Timestamp.fromDate(now.add(const Duration(days: 8))),
          'numberOfGuests': 4,
          'numberOfNights': 3,
          'roomRate': 8000.0,
          'totalAmount': 24000.0,
          'status': 'Pending',
          'paymentStatus': 'Pending',
          'specialRequests': 'Airport pickup required',
          'aadharVerified': false,
        },
      ];

      // Add bookings to Firestore
      for (final booking in sampleBookings) {
        await addBooking(booking);
      }

      if (kDebugMode) print('Sample bookings initialized successfully in Firestore');
    } catch (e) {
      if (kDebugMode) print('Error initializing sample bookings: $e');
    }
  }

  /// Get booking status options
  List<String> getBookingStatusOptions() {
    return [
      'Pending',
      'Confirmed',
      'Checked-In',
      'Checked-Out',
      'Cancelled',
      'No-Show',
    ];
  }

  /// Stream bookings for real-time updates
  Stream<List<Map<String, dynamic>>> streamUserBookings() {
    if (_currentUserId == null) {
      return Stream.value([]);
    }

    return _bookingsCollection.orderBy('createdAt', descending: true).snapshots().map((snapshot) {
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return data;
      }).toList();
    });
  }

  /// Clear all user booking data (for logout)
  Future<void> clearUserBookingData() async {
    // For Firestore, we don't need to clear data on logout
    // Data remains in the cloud and will be available on next login
    if (kDebugMode) print('Booking data remains in Firestore for next login');
  }
}
