import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';

class RealtimeSyncService {
  static final RealtimeSyncService _instance = RealtimeSyncService._internal();
  factory RealtimeSyncService() => _instance;
  RealtimeSyncService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  // Stream controllers for real-time updates
  final _bookingsController = StreamController<List<Map<String, dynamic>>>.broadcast();
  final _roomsController = StreamController<List<Map<String, dynamic>>>.broadcast();
  final _guestsController = StreamController<List<Map<String, dynamic>>>.broadcast();
  final _analyticsController = StreamController<Map<String, dynamic>>.broadcast();

  // Stream subscriptions
  StreamSubscription<QuerySnapshot>? _bookingsSubscription;
  StreamSubscription<QuerySnapshot>? _roomsSubscription;
  StreamSubscription<QuerySnapshot>? _guestsSubscription;

  // Getters for streams
  Stream<List<Map<String, dynamic>>> get bookingsStream => _bookingsController.stream;
  Stream<List<Map<String, dynamic>>> get roomsStream => _roomsController.stream;
  Stream<List<Map<String, dynamic>>> get guestsStream => _guestsController.stream;
  Stream<Map<String, dynamic>> get analyticsStream => _analyticsController.stream;

  /// Initialize real-time synchronization for a vendor
  void initializeSync(String vendorId) {
    _startBookingsSync(vendorId);
    _startRoomsSync(vendorId);
    _startGuestsSync(vendorId);
    _startAnalyticsSync(vendorId);
  }

  /// Start real-time booking synchronization
  void _startBookingsSync(String vendorId) {
    _bookingsSubscription?.cancel();
    
    _bookingsSubscription = _firestore
        .collection('hotel_bookings')
        .where('vendorId', isEqualTo: vendorId)
        .orderBy('bookingDate', descending: true)
        .snapshots()
        .listen((snapshot) {
      final bookings = snapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
      
      _bookingsController.add(bookings);
      
      // Trigger analytics update when bookings change
      _updateAnalytics(vendorId);
    });
  }

  /// Start real-time room synchronization
  void _startRoomsSync(String vendorId) {
    _roomsSubscription?.cancel();
    
    _roomsSubscription = _firestore
        .collection('hotel_rooms')
        .where('vendorId', isEqualTo: vendorId)
        .snapshots()
        .listen((snapshot) {
      final rooms = snapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
      
      _roomsController.add(rooms);
    });
  }

  /// Start real-time guest synchronization
  void _startGuestsSync(String vendorId) {
    _guestsSubscription?.cancel();
    
    // Get current guests (checked in)
    _guestsSubscription = _firestore
        .collection('hotel_bookings')
        .where('vendorId', isEqualTo: vendorId)
        .where('status', isEqualTo: 'checked_in')
        .snapshots()
        .listen((snapshot) {
      final guests = snapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
      
      _guestsController.add(guests);
    });
  }

  /// Start real-time analytics synchronization
  void _startAnalyticsSync(String vendorId) {
    // Update analytics every 30 seconds
    Timer.periodic(const Duration(seconds: 30), (timer) {
      _updateAnalytics(vendorId);
    });
    
    // Initial analytics update
    _updateAnalytics(vendorId);
  }

  /// Update analytics data
  Future<void> _updateAnalytics(String vendorId) async {
    try {
      final now = DateTime.now();
      final startOfMonth = DateTime(now.year, now.month, 1);
      final endOfMonth = DateTime(now.year, now.month + 1, 0, 23, 59, 59);

      // Get monthly bookings
      final monthlyQuery = await _firestore
          .collection('hotel_bookings')
          .where('vendorId', isEqualTo: vendorId)
          .where('bookingDate', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfMonth))
          .where('bookingDate', isLessThanOrEqualTo: Timestamp.fromDate(endOfMonth))
          .get();

      // Calculate monthly revenue
      double monthlyRevenue = 0.0;
      for (final doc in monthlyQuery.docs) {
        final data = doc.data();
        monthlyRevenue += (data['totalAmount'] ?? 0.0).toDouble();
      }

      // Get total bookings and revenue
      final totalQuery = await _firestore
          .collection('hotel_bookings')
          .where('vendorId', isEqualTo: vendorId)
          .get();

      double totalRevenue = 0.0;
      for (final doc in totalQuery.docs) {
        final data = doc.data();
        totalRevenue += (data['totalAmount'] ?? 0.0).toDouble();
      }

      // Get current occupancy
      final currentBookings = await _firestore
          .collection('hotel_bookings')
          .where('vendorId', isEqualTo: vendorId)
          .where('status', whereIn: ['confirmed', 'checked_in'])
          .where('checkInDate', isLessThanOrEqualTo: Timestamp.fromDate(now))
          .where('checkOutDate', isGreaterThan: Timestamp.fromDate(now))
          .get();

      // Get total rooms
      final roomsQuery = await _firestore
          .collection('hotel_rooms')
          .where('vendorId', isEqualTo: vendorId)
          .get();

      final totalRooms = roomsQuery.docs.length;
      final occupiedRooms = currentBookings.docs.length;
      final occupancyRate = totalRooms > 0 ? (occupiedRooms / totalRooms) * 100 : 0.0;

      // Get today's check-ins and check-outs
      final today = DateTime.now();
      final startOfDay = DateTime(today.year, today.month, today.day);
      final endOfDay = DateTime(today.year, today.month, today.day, 23, 59, 59);

      final todayCheckIns = await _firestore
          .collection('hotel_bookings')
          .where('vendorId', isEqualTo: vendorId)
          .where('checkInDate', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
          .where('checkInDate', isLessThanOrEqualTo: Timestamp.fromDate(endOfDay))
          .where('status', isEqualTo: 'confirmed')
          .get();

      final todayCheckOuts = await _firestore
          .collection('hotel_bookings')
          .where('vendorId', isEqualTo: vendorId)
          .where('checkOutDate', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
          .where('checkOutDate', isLessThanOrEqualTo: Timestamp.fromDate(endOfDay))
          .where('status', isEqualTo: 'checked_in')
          .get();

      final analytics = {
        'monthlyBookings': monthlyQuery.docs.length,
        'totalBookings': totalQuery.docs.length,
        'monthlyRevenue': monthlyRevenue,
        'totalRevenue': totalRevenue,
        'averageBookingValue': totalQuery.docs.isNotEmpty 
            ? totalRevenue / totalQuery.docs.length 
            : 0.0,
        'occupancyRate': occupancyRate,
        'totalRooms': totalRooms,
        'occupiedRooms': occupiedRooms,
        'availableRooms': totalRooms - occupiedRooms,
        'todayCheckIns': todayCheckIns.docs.length,
        'todayCheckOuts': todayCheckOuts.docs.length,
        'lastUpdated': DateTime.now().toIso8601String(),
      };

      _analyticsController.add(analytics);
    } catch (e) {
      print('Error updating analytics: $e');
    }
  }

  /// Sync booking status across all apps
  Future<void> syncBookingStatus(String bookingId, String newStatus) async {
    try {
      await _firestore
          .collection('hotel_bookings')
          .doc(bookingId)
          .update({
        'status': newStatus,
        'updatedAt': Timestamp.now(),
        'lastSyncedAt': Timestamp.now(),
      });

      // Trigger immediate sync for all connected clients
      await _triggerImmediateSync(bookingId);
    } catch (e) {
      print('Error syncing booking status: $e');
    }
  }

  /// Sync room availability across all apps
  Future<void> syncRoomAvailability(String roomId, bool isAvailable) async {
    try {
      await _firestore
          .collection('hotel_rooms')
          .doc(roomId)
          .update({
        'isAvailable': isAvailable,
        'updatedAt': Timestamp.now(),
        'lastSyncedAt': Timestamp.now(),
      });
    } catch (e) {
      print('Error syncing room availability: $e');
    }
  }

  /// Sync guest check-in/out across all apps
  Future<void> syncGuestStatus(String bookingId, String status, {String? aadharNumber}) async {
    try {
      final updates = <String, dynamic>{
        'status': status,
        'updatedAt': Timestamp.now(),
        'lastSyncedAt': Timestamp.now(),
      };

      if (status == 'checked_in') {
        updates['actualCheckInTime'] = Timestamp.now();
        if (aadharNumber != null) {
          updates['aadharNumber'] = aadharNumber;
          updates['aadharVerified'] = true;
          updates['aadharVerificationTime'] = Timestamp.now();
        }
      } else if (status == 'checked_out') {
        updates['actualCheckOutTime'] = Timestamp.now();
      }

      await _firestore
          .collection('hotel_bookings')
          .doc(bookingId)
          .update(updates);

      // Trigger immediate sync
      await _triggerImmediateSync(bookingId);
    } catch (e) {
      print('Error syncing guest status: $e');
    }
  }

  /// Trigger immediate sync for a specific booking
  Future<void> _triggerImmediateSync(String bookingId) async {
    try {
      // Create a sync trigger document
      await _firestore
          .collection('sync_triggers')
          .add({
        'bookingId': bookingId,
        'timestamp': Timestamp.now(),
        'type': 'booking_update',
      });
    } catch (e) {
      print('Error triggering immediate sync: $e');
    }
  }

  /// Listen for sync triggers from other apps
  void listenForSyncTriggers(String vendorId) {
    _firestore
        .collection('sync_triggers')
        .where('timestamp', isGreaterThan: Timestamp.now())
        .snapshots()
        .listen((snapshot) {
      for (final doc in snapshot.docs) {
        final data = doc.data();
        final bookingId = data['bookingId'];
        
        // Refresh specific booking data
        _refreshBookingData(bookingId);
        
        // Delete the trigger after processing
        doc.reference.delete();
      }
    });
  }

  /// Refresh specific booking data
  Future<void> _refreshBookingData(String bookingId) async {
    try {
      final doc = await _firestore
          .collection('hotel_bookings')
          .doc(bookingId)
          .get();
      
      if (doc.exists) {
        // Trigger a refresh of the bookings stream
        // This will be handled by the existing stream listeners
      }
    } catch (e) {
      print('Error refreshing booking data: $e');
    }
  }

  /// Get real-time hotel statistics
  Stream<Map<String, dynamic>> getHotelStatistics(String hotelId) {
    return _firestore
        .collection('hotel_bookings')
        .where('hotelId', isEqualTo: hotelId)
        .snapshots()
        .map((snapshot) {
      final bookings = snapshot.docs;
      final totalBookings = bookings.length;
      
      double totalRevenue = 0.0;
      int confirmedBookings = 0;
      int checkedInGuests = 0;
      
      for (final doc in bookings) {
        final data = doc.data();
        totalRevenue += (data['totalAmount'] ?? 0.0).toDouble();
        
        final status = data['status'] ?? '';
        if (status == 'confirmed') confirmedBookings++;
        if (status == 'checked_in') checkedInGuests++;
      }
      
      return {
        'totalBookings': totalBookings,
        'totalRevenue': totalRevenue,
        'confirmedBookings': confirmedBookings,
        'checkedInGuests': checkedInGuests,
        'averageBookingValue': totalBookings > 0 ? totalRevenue / totalBookings : 0.0,
      };
    });
  }

  /// Dispose of all streams and subscriptions
  void dispose() {
    _bookingsSubscription?.cancel();
    _roomsSubscription?.cancel();
    _guestsSubscription?.cancel();
    
    _bookingsController.close();
    _roomsController.close();
    _guestsController.close();
    _analyticsController.close();
  }
}
