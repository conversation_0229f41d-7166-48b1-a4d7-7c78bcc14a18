-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_analytics-11.5.2\android\src\main\AndroidManifest.xml:1:1-14:12
INJECTED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_analytics-11.5.2\android\src\main\AndroidManifest.xml:1:1-14:12
	package
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_analytics-11.5.2\android\src\main\AndroidManifest.xml:2:3-50
		INJECTED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_analytics-11.5.2\android\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_analytics-11.5.2\android\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_analytics-11.5.2\android\src\main\AndroidManifest.xml:4:3-77
	android:name
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_analytics-11.5.2\android\src\main\AndroidManifest.xml:4:20-74
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_analytics-11.5.2\android\src\main\AndroidManifest.xml:5:3-65
	android:name
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_analytics-11.5.2\android\src\main\AndroidManifest.xml:5:20-62
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_analytics-11.5.2\android\src\main\AndroidManifest.xml:6:3-66
	android:name
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_analytics-11.5.2\android\src\main\AndroidManifest.xml:6:20-63
application
ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_analytics-11.5.2\android\src\main\AndroidManifest.xml:8:3-13:17
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_analytics-11.5.2\android\src\main\AndroidManifest.xml:9:5-12:15
	android:name
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_analytics-11.5.2\android\src\main\AndroidManifest.xml:9:14-85
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.analytics.FlutterFirebaseAppRegistrar
ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_analytics-11.5.2\android\src\main\AndroidManifest.xml:10:7-11:86
	android:value
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_analytics-11.5.2\android\src\main\AndroidManifest.xml:11:18-83
	android:name
		ADDED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_analytics-11.5.2\android\src\main\AndroidManifest.xml:10:18-129
uses-sdk
INJECTED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_analytics-11.5.2\android\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_analytics-11.5.2\android\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_analytics-11.5.2\android\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_analytics-11.5.2\android\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_analytics-11.5.2\android\src\main\AndroidManifest.xml
