import 'package:flutter/material.dart';
import '../../config/app_theme.dart';
import '../../models/vendor_models.dart';
import '../../services/hotel_registration_service.dart';

class AdminPanelScreen extends StatefulWidget {
  const AdminPanelScreen({super.key});

  @override
  State<AdminPanelScreen> createState() => _AdminPanelScreenState();
}

class _AdminPanelScreenState extends State<AdminPanelScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _registrationService = HotelRegistrationService();

  List<HotelVendor> _pendingVendors = [];
  List<HotelRegistration> _pendingHotels = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadPendingApplications();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadPendingApplications() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final vendors = await _registrationService.getPendingVendors();
      final hotels = await _registrationService.getPendingHotels();

      setState(() {
        _pendingVendors = vendors;
        _pendingHotels = hotels;
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading applications: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Admin Panel'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: [
            Tab(
              text: 'Vendor Applications',
              icon: Badge(
                label: Text('${_pendingVendors.length}'),
                child: const Icon(Icons.business),
              ),
            ),
            Tab(
              text: 'Hotel Applications',
              icon: Badge(
                label: Text('${_pendingHotels.length}'),
                child: const Icon(Icons.hotel),
              ),
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadPendingApplications,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildVendorApplicationsTab(),
                _buildHotelApplicationsTab(),
              ],
            ),
    );
  }

  Widget _buildVendorApplicationsTab() {
    if (_pendingVendors.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.check_circle_outline,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'No Pending Vendor Applications',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'All vendor applications have been processed',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _pendingVendors.length,
      itemBuilder: (context, index) {
        final vendor = _pendingVendors[index];
        return _buildVendorCard(vendor);
      },
    );
  }

  Widget _buildHotelApplicationsTab() {
    if (_pendingHotels.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.check_circle_outline,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'No Pending Hotel Applications',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'All hotel applications have been processed',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _pendingHotels.length,
      itemBuilder: (context, index) {
        final hotel = _pendingHotels[index];
        return _buildHotelCard(hotel);
      },
    );
  }

  Widget _buildVendorCard(HotelVendor vendor) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: AppTheme.primaryColor,
                  child: Text(
                    vendor.firstName[0].toUpperCase(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        vendor.fullName,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        vendor.businessName,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.orange[100],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'PENDING',
                    style: TextStyle(
                      color: Colors.orange[800],
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow('Email', vendor.email),
            _buildInfoRow('Phone', vendor.phone),
            _buildInfoRow('PAN', vendor.panNumber),
            _buildInfoRow('GST', vendor.gstNumber),
            _buildInfoRow('Address', vendor.address.fullAddress),
            _buildInfoRow('Applied', _formatDate(vendor.createdAt)),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _rejectVendor(vendor),
                    icon: const Icon(Icons.close, color: Colors.red),
                    label: const Text('Reject', style: TextStyle(color: Colors.red)),
                    style: OutlinedButton.styleFrom(
                      side: const BorderSide(color: Colors.red),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _approveVendor(vendor),
                    icon: const Icon(Icons.check, color: Colors.white),
                    label: const Text('Approve'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHotelCard(HotelRegistration hotel) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.hotel,
                    color: Colors.white,
                    size: 30,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        hotel.hotelName,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${hotel.address.city}, ${hotel.address.state}',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                      Row(
                        children: [
                          ...List.generate(hotel.starRating, (index) {
                            return const Icon(
                              Icons.star,
                              color: Colors.amber,
                              size: 16,
                            );
                          }),
                          const SizedBox(width: 8),
                          Text(
                            _getHotelTypeDisplayName(hotel.hotelType),
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.orange[100],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'PENDING',
                    style: TextStyle(
                      color: Colors.orange[800],
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              hotel.description,
              style: TextStyle(
                color: Colors.grey[700],
                fontSize: 14,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 12),
            _buildInfoRow('Address', hotel.address.fullAddress),
            _buildInfoRow('Contact', '${hotel.contactInfo.email} • ${hotel.contactInfo.phone}'),
            _buildInfoRow('Room Types', '${hotel.roomTypes.length} types'),
            _buildInfoRow('Amenities', '${hotel.amenities.length} amenities'),
            _buildInfoRow('Applied', _formatDate(hotel.createdAt)),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _rejectHotel(hotel),
                    icon: const Icon(Icons.close, color: Colors.red),
                    label: const Text('Reject', style: TextStyle(color: Colors.red)),
                    style: OutlinedButton.styleFrom(
                      side: const BorderSide(color: Colors.red),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _approveHotel(hotel),
                    icon: const Icon(Icons.check, color: Colors.white),
                    label: const Text('Approve'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _approveVendor(HotelVendor vendor) async {
    final confirmed = await _showConfirmationDialog(
      'Approve Vendor',
      'Are you sure you want to approve ${vendor.fullName}?',
    );

    if (confirmed) {
      try {
        await _registrationService.updateVendorStatus(vendor.id, VendorStatus.verified);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${vendor.fullName} has been approved'),
            backgroundColor: Colors.green,
          ),
        );
        _loadPendingApplications();
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error approving vendor: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _rejectVendor(HotelVendor vendor) async {
    final confirmed = await _showConfirmationDialog(
      'Reject Vendor',
      'Are you sure you want to reject ${vendor.fullName}?',
    );

    if (confirmed) {
      try {
        await _registrationService.updateVendorStatus(vendor.id, VendorStatus.rejected);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${vendor.fullName} has been rejected'),
            backgroundColor: Colors.orange,
          ),
        );
        _loadPendingApplications();
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error rejecting vendor: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _approveHotel(HotelRegistration hotel) async {
    final confirmed = await _showConfirmationDialog(
      'Approve Hotel',
      'Are you sure you want to approve ${hotel.hotelName}?',
    );

    if (confirmed) {
      try {
        await _registrationService.updateHotelStatus(hotel.id, HotelStatus.approved);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${hotel.hotelName} has been approved'),
            backgroundColor: Colors.green,
          ),
        );
        _loadPendingApplications();
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error approving hotel: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _rejectHotel(HotelRegistration hotel) async {
    final confirmed = await _showConfirmationDialog(
      'Reject Hotel',
      'Are you sure you want to reject ${hotel.hotelName}?',
    );

    if (confirmed) {
      try {
        await _registrationService.updateHotelStatus(hotel.id, HotelStatus.rejected);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${hotel.hotelName} has been rejected'),
            backgroundColor: Colors.orange,
          ),
        );
        _loadPendingApplications();
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error rejecting hotel: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<bool> _showConfirmationDialog(String title, String message) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Confirm'),
          ),
        ],
      ),
    ) ?? false;
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _getHotelTypeDisplayName(HotelType type) {
    switch (type) {
      case HotelType.hotel:
        return 'Hotel';
      case HotelType.resort:
        return 'Resort';
      case HotelType.guesthouse:
        return 'Guest House';
      case HotelType.hostel:
        return 'Hostel';
      case HotelType.apartment:
        return 'Apartment';
      case HotelType.villa:
        return 'Villa';
      case HotelType.homestay:
        return 'Homestay';
    }
  }
}
