import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'hybrid_auth_service.dart';

/// Firebase Firestore service for rooms management
class FirebaseRoomsService {
  static final FirebaseRoomsService _instance = FirebaseRoomsService._internal();
  factory FirebaseRoomsService() => _instance;
  FirebaseRoomsService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final HybridAuthService _authService = HybridAuthService();

  /// Get current user ID
  String? get _currentUserId => _authService.currentUser?['id'];

  /// Get user's rooms collection reference
  CollectionReference get _roomsCollection {
    if (_currentUserId == null) {
      throw Exception('User not authenticated');
    }
    return _firestore.collection('vendors').doc(_currentUserId).collection('rooms');
  }

  /// Get all rooms for current user
  Future<List<Map<String, dynamic>>> getUserRooms() async {
    try {
      if (_currentUserId == null) return [];

      final querySnapshot = await _roomsCollection.get();
      return querySnapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      if (kDebugMode) print('Error getting user rooms: $e');
      return [];
    }
  }

  /// Get rooms by status
  Future<List<Map<String, dynamic>>> getRoomsByStatus(String status) async {
    try {
      if (_currentUserId == null) return [];

      Query query = _roomsCollection;
      if (status != 'all') {
        query = query.where('status', isEqualTo: status);
      }

      final querySnapshot = await query.get();
      return querySnapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      if (kDebugMode) print('Error getting rooms by status: $e');
      return [];
    }
  }

  /// Add new room
  Future<bool> addRoom(Map<String, dynamic> roomData) async {
    try {
      if (_currentUserId == null) return false;

      final newRoomData = {
        ...roomData,
        'vendorId': _currentUserId,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      };

      await _roomsCollection.add(newRoomData);
      
      if (kDebugMode) print('Room added successfully to Firestore');
      return true;
    } catch (e) {
      if (kDebugMode) print('Error adding room to Firestore: $e');
      return false;
    }
  }

  /// Update room
  Future<bool> updateRoom(String roomId, Map<String, dynamic> updates) async {
    try {
      if (_currentUserId == null) return false;

      final updateData = {
        ...updates,
        'updatedAt': FieldValue.serverTimestamp(),
      };

      await _roomsCollection.doc(roomId).update(updateData);
      
      if (kDebugMode) print('Room updated successfully in Firestore');
      return true;
    } catch (e) {
      if (kDebugMode) print('Error updating room in Firestore: $e');
      return false;
    }
  }

  /// Delete room
  Future<bool> deleteRoom(String roomId) async {
    try {
      if (_currentUserId == null) return false;

      await _roomsCollection.doc(roomId).delete();
      
      if (kDebugMode) print('Room deleted successfully from Firestore');
      return true;
    } catch (e) {
      if (kDebugMode) print('Error deleting room from Firestore: $e');
      return false;
    }
  }

  /// Get room by ID
  Future<Map<String, dynamic>?> getRoomById(String roomId) async {
    try {
      if (_currentUserId == null) return null;

      final doc = await _roomsCollection.doc(roomId).get();
      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return data;
      }
      return null;
    } catch (e) {
      if (kDebugMode) print('Error getting room by ID: $e');
      return null;
    }
  }

  /// Update room status
  Future<bool> updateRoomStatus(String roomId, String status) async {
    return await updateRoom(roomId, {'status': status});
  }

  /// Get room statistics
  Future<Map<String, dynamic>> getRoomStatistics() async {
    try {
      if (_currentUserId == null) {
        return {
          'total': 0,
          'available': 0,
          'occupied': 0,
          'maintenance': 0,
          'cleaning': 0,
          'occupancyRate': 0.0,
        };
      }

      final rooms = await getUserRooms();
      
      final available = rooms.where((r) => r['status']?.toLowerCase() == 'available').length;
      final occupied = rooms.where((r) => r['status']?.toLowerCase() == 'occupied').length;
      final maintenance = rooms.where((r) => r['status']?.toLowerCase() == 'maintenance').length;
      final cleaning = rooms.where((r) => r['status']?.toLowerCase() == 'cleaning').length;
      
      return {
        'total': rooms.length,
        'available': available,
        'occupied': occupied,
        'maintenance': maintenance,
        'cleaning': cleaning,
        'occupancyRate': rooms.isNotEmpty ? (occupied / rooms.length) * 100 : 0.0,
      };
    } catch (e) {
      if (kDebugMode) print('Error getting room statistics: $e');
      return {
        'total': 0,
        'available': 0,
        'occupied': 0,
        'maintenance': 0,
        'cleaning': 0,
        'occupancyRate': 0.0,
      };
    }
  }

  /// Search rooms
  Future<List<Map<String, dynamic>>> searchRooms(String searchTerm) async {
    try {
      if (_currentUserId == null) return [];

      // Firestore doesn't support full-text search, so we'll get all rooms and filter locally
      final rooms = await getUserRooms();
      final searchLower = searchTerm.toLowerCase();
      
      return rooms.where((room) {
        final roomNumber = (room['roomNumber'] ?? room['id'] ?? '').toString().toLowerCase();
        final roomType = (room['type'] ?? '').toString().toLowerCase();
        final status = (room['status'] ?? '').toString().toLowerCase();
        
        return roomNumber.contains(searchLower) ||
               roomType.contains(searchLower) ||
               status.contains(searchLower);
      }).toList();
    } catch (e) {
      if (kDebugMode) print('Error searching rooms: $e');
      return [];
    }
  }

  /// Initialize user with sample rooms (only for new users)
  Future<void> initializeNewUserRooms() async {
    try {
      if (_currentUserId == null) return;

      final existingRooms = await getUserRooms();
      if (existingRooms.isNotEmpty) return; // User already has rooms

      if (kDebugMode) print('Initializing sample rooms in Firestore for new user');

      // Add some initial rooms for demonstration
      final sampleRooms = [
        {
          'roomNumber': '101',
          'type': 'Deluxe Room',
          'status': 'Available',
          'price': 5000.0,
          'capacity': 2,
          'amenities': ['WiFi', 'AC', 'TV', 'Mini Bar'],
          'description': 'Spacious deluxe room with modern amenities',
          'floor': 1,
          'bedType': 'King Size',
          'image': 'https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=400&h=300&fit=crop',
        },
        {
          'roomNumber': '102',
          'type': 'Standard Room',
          'status': 'Occupied',
          'price': 3500.0,
          'capacity': 2,
          'amenities': ['WiFi', 'AC', 'TV'],
          'description': 'Comfortable standard room with essential amenities',
          'floor': 1,
          'bedType': 'Queen Size',
          'image': 'https://images.unsplash.com/photo-1522771739844-6a9f6d5f14af?w=400&h=300&fit=crop',
        },
        {
          'roomNumber': '201',
          'type': 'Suite',
          'status': 'Available',
          'price': 8000.0,
          'capacity': 4,
          'amenities': ['WiFi', 'AC', 'TV', 'Mini Bar', 'Balcony', 'Jacuzzi'],
          'description': 'Luxurious suite with premium amenities and city view',
          'floor': 2,
          'bedType': 'King Size',
          'image': 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',
        },
        {
          'roomNumber': '202',
          'type': 'Executive Room',
          'status': 'Maintenance',
          'price': 6000.0,
          'capacity': 2,
          'amenities': ['WiFi', 'AC', 'TV', 'Mini Bar', 'Work Desk'],
          'description': 'Executive room perfect for business travelers',
          'floor': 2,
          'bedType': 'King Size',
          'image': 'https://images.unsplash.com/photo-1618773928121-c32242e63f39?w=400&h=300&fit=crop',
        },
      ];

      // Add rooms to Firestore
      for (final room in sampleRooms) {
        await addRoom(room);
      }

      if (kDebugMode) print('Sample rooms initialized successfully in Firestore');
    } catch (e) {
      if (kDebugMode) print('Error initializing sample rooms: $e');
    }
  }

  /// Get available room types
  List<String> getAvailableRoomTypes() {
    return [
      'Standard Room',
      'Deluxe Room',
      'Suite',
      'Executive Room',
      'Presidential Suite',
      'Family Room',
      'Twin Room',
      'Single Room',
      'Double Room',
      'Triple Room',
    ];
  }

  /// Get available amenities
  List<String> getAvailableAmenities() {
    return [
      'WiFi',
      'AC',
      'TV',
      'Mini Bar',
      'Room Service',
      'Balcony',
      'Sea View',
      'City View',
      'Bathtub',
      'Shower',
      'Safe',
      'Telephone',
      'Hair Dryer',
      'Iron',
      'Coffee Maker',
      'Refrigerator',
      'Work Desk',
      'Jacuzzi',
      'Kitchenette',
    ];
  }

  /// Get room status options
  List<String> getRoomStatusOptions() {
    return [
      'Available',
      'Occupied',
      'Maintenance',
      'Cleaning',
      'Reserved',
      'Out of Order',
    ];
  }

  /// Stream rooms for real-time updates
  Stream<List<Map<String, dynamic>>> streamUserRooms() {
    if (_currentUserId == null) {
      return Stream.value([]);
    }

    return _roomsCollection.snapshots().map((snapshot) {
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return data;
      }).toList();
    });
  }

  /// Clear all user room data (for logout)
  Future<void> clearUserRoomData() async {
    // For Firestore, we don't need to clear data on logout
    // Data remains in the cloud and will be available on next login
    if (kDebugMode) print('Room data remains in Firestore for next login');
  }
}
