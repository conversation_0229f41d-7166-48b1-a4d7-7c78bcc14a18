<svg width="375" height="812" viewBox="0 0 375 812" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Sunset gradient -->
  <defs>
    <linearGradient id="bg3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff9a9e;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#fecfef;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fecfef;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="sun" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#FFA500;stop-opacity:0.4" />
    </radialGradient>
  </defs>
  
  <!-- Background -->
  <rect width="375" height="812" fill="url(#bg3)"/>
  
  <!-- Mountains silhouette -->
  <path d="M0,500 L75,400 L150,450 L225,350 L300,400 L375,380 L375,812 L0,812 Z" fill="#8B4B8B" opacity="0.3"/>
  <path d="M0,550 L100,480 L200,520 L300,450 L375,480 L375,812 L0,812 Z" fill="#9B5B9B" opacity="0.2"/>
  
  <!-- Sun -->
  <circle cx="280" cy="200" r="40" fill="url(#sun)"/>
  
  <!-- Birds -->
  <g transform="translate(100, 250)">
    <path d="M0,0 Q5,-3 10,0 Q5,3 0,0" fill="#333" opacity="0.3"/>
  </g>
  <g transform="translate(150, 280)">
    <path d="M0,0 Q5,-3 10,0 Q5,3 0,0" fill="#333" opacity="0.3"/>
  </g>
  <g transform="translate(200, 260)">
    <path d="M0,0 Q5,-3 10,0 Q5,3 0,0" fill="#333" opacity="0.3"/>
  </g>
  
  <!-- Clouds -->
  <g transform="translate(50, 150)">
    <circle cx="0" cy="0" r="15" fill="white" opacity="0.4"/>
    <circle cx="20" cy="0" r="20" fill="white" opacity="0.4"/>
    <circle cx="40" cy="0" r="15" fill="white" opacity="0.4"/>
  </g>
  
  <!-- Stars -->
  <g transform="translate(320, 100)">
    <path d="M0,-5 L1.5,-1.5 L5,0 L1.5,1.5 L0,5 L-1.5,1.5 L-5,0 L-1.5,-1.5 Z" fill="white" opacity="0.6"/>
  </g>
  <g transform="translate(80, 80)">
    <path d="M0,-3 L1,-1 L3,0 L1,1 L0,3 L-1,1 L-3,0 L-1,-1 Z" fill="white" opacity="0.5"/>
  </g>
</svg>
