import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/hotel.dart';

class VendorIntegrationService {
  static final VendorIntegrationService _instance = VendorIntegrationService._internal();
  factory VendorIntegrationService() => _instance;
  VendorIntegrationService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Collections
  static const String hotelsCollection = 'hotel_registrations';
  static const String bookingsCollection = 'hotel_bookings';
  static const String vendorsCollection = 'hotel_vendors';

  /// Get approved vendor hotels for customer search
  Future<List<Hotel>> getApprovedVendorHotels({
    String? city,
    String? state,
    int? minStarRating,
    double? maxPrice,
    List<String>? amenities,
  }) async {
    try {
      Query query = _firestore
          .collection(hotelsCollection)
          .where('status', isEqualTo: 'approved');

      // Apply filters
      if (city != null && city.isNotEmpty) {
        query = query.where('address.city', isEqualTo: city);
      }
      if (state != null && state.isNotEmpty) {
        query = query.where('address.state', isEqualTo: state);
      }
      if (minStarRating != null) {
        query = query.where('starRating', isGreaterThanOrEqualTo: minStarRating);
      }

      final querySnapshot = await query.get();
      final vendorHotels = <Hotel>[];

      for (final doc in querySnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        
        // Convert vendor hotel to customer hotel format
        final hotel = _convertVendorHotelToCustomerHotel(doc.id, data);
        
        // Apply additional filters
        if (maxPrice != null) {
          final minRoomPrice = _getMinRoomPrice(data['roomTypes'] ?? []);
          if (minRoomPrice > maxPrice) continue;
        }
        
        if (amenities != null && amenities.isNotEmpty) {
          final hotelAmenities = List<String>.from(data['amenities'] ?? []);
          if (!amenities.every((amenity) => hotelAmenities.contains(amenity))) {
            continue;
          }
        }
        
        vendorHotels.add(hotel);
      }

      return vendorHotels;
    } catch (e) {
      print('Error getting vendor hotels: $e');
      return [];
    }
  }

  /// Get vendor hotel by ID
  Future<Hotel?> getVendorHotelById(String hotelId) async {
    try {
      final doc = await _firestore
          .collection(hotelsCollection)
          .doc(hotelId)
          .get();

      if (doc.exists && doc.data()?['status'] == 'approved') {
        return _convertVendorHotelToCustomerHotel(doc.id, doc.data()!);
      }
      return null;
    } catch (e) {
      print('Error getting vendor hotel by ID: $e');
      return null;
    }
  }

  /// Search vendor hotels by location
  Future<List<Hotel>> searchVendorHotelsByLocation(
    double latitude,
    double longitude,
    double radiusKm,
  ) async {
    try {
      // For now, we'll do a simple city-based search
      // In production, you'd use geospatial queries
      final allHotels = await getApprovedVendorHotels();
      
      // Filter by approximate location (simplified)
      return allHotels.where((hotel) {
        // This is a simplified distance check
        // In production, use proper geospatial calculations
        return true; // Return all for now
      }).toList();
    } catch (e) {
      print('Error searching hotels by location: $e');
      return [];
    }
  }

  /// Create booking for vendor hotel
  Future<String?> createVendorHotelBooking({
    required String hotelId,
    required String vendorId,
    required String customerId,
    required String customerName,
    required String customerEmail,
    required String customerPhone,
    required String roomTypeId,
    required DateTime checkInDate,
    required DateTime checkOutDate,
    required int numberOfGuests,
    required double totalAmount,
    required Map<String, dynamic> roomDetails,
  }) async {
    try {
      final booking = {
        'hotelId': hotelId,
        'vendorId': vendorId,
        'customerId': customerId,
        'customerName': customerName,
        'customerEmail': customerEmail,
        'customerPhone': customerPhone,
        'roomTypeId': roomTypeId,
        'checkInDate': Timestamp.fromDate(checkInDate),
        'checkOutDate': Timestamp.fromDate(checkOutDate),
        'numberOfGuests': numberOfGuests,
        'totalAmount': totalAmount,
        'roomDetails': roomDetails,
        'status': 'confirmed',
        'bookingDate': Timestamp.now(),
        'paymentStatus': 'pending',
        'source': 'customer_app',
      };

      final docRef = await _firestore
          .collection(bookingsCollection)
          .add(booking);

      return docRef.id;
    } catch (e) {
      print('Error creating vendor hotel booking: $e');
      return null;
    }
  }

  /// Get customer bookings for vendor hotels
  Future<List<Map<String, dynamic>>> getCustomerVendorBookings(String customerId) async {
    try {
      final query = await _firestore
          .collection(bookingsCollection)
          .where('customerId', isEqualTo: customerId)
          .where('source', isEqualTo: 'customer_app')
          .orderBy('bookingDate', descending: true)
          .get();

      return query.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      print('Error getting customer vendor bookings: $e');
      return [];
    }
  }

  /// Update booking status
  Future<bool> updateBookingStatus(String bookingId, String status) async {
    try {
      await _firestore
          .collection(bookingsCollection)
          .doc(bookingId)
          .update({
        'status': status,
        'updatedAt': Timestamp.now(),
      });
      return true;
    } catch (e) {
      print('Error updating booking status: $e');
      return false;
    }
  }

  /// Get vendor hotel reviews
  Future<List<Map<String, dynamic>>> getVendorHotelReviews(String hotelId) async {
    try {
      // This would integrate with a reviews collection
      // For now, return mock data
      return [
        {
          'id': '1',
          'customerName': 'John Doe',
          'rating': 4.5,
          'comment': 'Great hotel with excellent service!',
          'date': DateTime.now().subtract(const Duration(days: 5)),
        },
        {
          'id': '2',
          'customerName': 'Jane Smith',
          'rating': 5.0,
          'comment': 'Amazing experience, highly recommended!',
          'date': DateTime.now().subtract(const Duration(days: 10)),
        },
      ];
    } catch (e) {
      print('Error getting vendor hotel reviews: $e');
      return [];
    }
  }

  /// Submit review for vendor hotel
  Future<bool> submitVendorHotelReview({
    required String hotelId,
    required String customerId,
    required String customerName,
    required double rating,
    required String comment,
  }) async {
    try {
      await _firestore
          .collection('hotel_reviews')
          .add({
        'hotelId': hotelId,
        'customerId': customerId,
        'customerName': customerName,
        'rating': rating,
        'comment': comment,
        'date': Timestamp.now(),
        'source': 'customer_app',
      });
      return true;
    } catch (e) {
      print('Error submitting vendor hotel review: $e');
      return false;
    }
  }

  /// Get available cities with vendor hotels
  Future<List<String>> getAvailableCities() async {
    try {
      final query = await _firestore
          .collection(hotelsCollection)
          .where('status', isEqualTo: 'approved')
          .get();

      final cities = <String>{};
      for (final doc in query.docs) {
        final data = doc.data();
        final city = data['address']?['city'];
        if (city != null && city.isNotEmpty) {
          cities.add(city);
        }
      }

      return cities.toList()..sort();
    } catch (e) {
      print('Error getting available cities: $e');
      return [];
    }
  }

  /// Convert vendor hotel data to customer hotel format
  Hotel _convertVendorHotelToCustomerHotel(String id, Map<String, dynamic> data) {
    // Extract room types and convert to customer format
    final roomTypes = (data['roomTypes'] as List? ?? []).map((roomType) {
      return {
        'id': roomType['name']?.toString().toLowerCase().replaceAll(' ', '_') ?? 'standard',
        'name': roomType['name'] ?? 'Standard Room',
        'description': roomType['description'] ?? '',
        'maxOccupancy': roomType['maxOccupancy'] ?? 2,
        'basePrice': (roomType['basePrice'] ?? 0.0).toDouble(),
        'amenities': List<String>.from(roomType['amenities'] ?? []),
        'images': _generateRoomImages(roomType['name'] ?? 'Standard Room'),
      };
    }).toList();

    // Generate hotel images based on hotel type and name
    final images = _generateHotelImages(data['hotelName'] ?? 'Hotel', data['hotelType'] ?? 'hotel');

    return Hotel(
      id: id,
      name: data['hotelName'] ?? 'Unknown Hotel',
      description: data['description'] ?? '',
      address: _formatAddress(data['address'] ?? {}),
      city: data['address']?['city'] ?? '',
      state: data['address']?['state'] ?? '',
      country: data['address']?['country'] ?? 'India',
      latitude: data['address']?['latitude']?.toDouble() ?? 0.0,
      longitude: data['address']?['longitude']?.toDouble() ?? 0.0,
      starRating: data['starRating'] ?? 3,
      rating: 4.2, // Default rating, would come from reviews
      reviewCount: 25, // Default review count
      images: images,
      amenities: List<String>.from(data['amenities'] ?? []),
      roomTypes: roomTypes,
      checkInTime: data['policies']?['checkInTime'] ?? '14:00',
      checkOutTime: data['policies']?['checkOutTime'] ?? '11:00',
      cancellationPolicy: data['policies']?['cancellationPolicy'] ?? 'Free cancellation up to 24 hours',
      contactEmail: data['contactInfo']?['email'] ?? '',
      contactPhone: data['contactInfo']?['phone'] ?? '',
      website: data['contactInfo']?['website'],
      isVendorHotel: true, // Mark as vendor hotel
      vendorId: data['vendorId'],
    );
  }

  /// Format address from vendor data
  String _formatAddress(Map<String, dynamic> address) {
    final parts = <String>[];
    if (address['street']?.isNotEmpty == true) parts.add(address['street']);
    if (address['city']?.isNotEmpty == true) parts.add(address['city']);
    if (address['state']?.isNotEmpty == true) parts.add(address['state']);
    if (address['pincode']?.isNotEmpty == true) parts.add(address['pincode']);
    return parts.join(', ');
  }

  /// Get minimum room price from room types
  double _getMinRoomPrice(List<dynamic> roomTypes) {
    if (roomTypes.isEmpty) return 0.0;
    
    double minPrice = double.infinity;
    for (final roomType in roomTypes) {
      final price = (roomType['basePrice'] ?? 0.0).toDouble();
      if (price < minPrice) {
        minPrice = price;
      }
    }
    
    return minPrice == double.infinity ? 0.0 : minPrice;
  }

  /// Generate hotel images based on name and type
  List<String> _generateHotelImages(String hotelName, String hotelType) {
    // This would integrate with the existing hotel image service
    // For now, return some default images
    return [
      'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800',
      'https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?w=800',
      'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=800',
    ];
  }

  /// Generate room images based on room type
  List<String> _generateRoomImages(String roomTypeName) {
    // This would integrate with the existing room image service
    return [
      'https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800',
      'https://images.unsplash.com/photo-1522771739844-6a9f6d5f14af?w=800',
    ];
  }
}
