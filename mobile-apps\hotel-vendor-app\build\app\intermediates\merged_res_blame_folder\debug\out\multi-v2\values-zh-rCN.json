{"logs": [{"outputFile": "com.example.hotel_vendor_app-mergeDebugResources-51:/values-zh-rCN/values-zh-rCN.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3016c2af0ee971c1feadddd0d6645a81\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,110", "endOffsets": "156,267"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2662,2768", "endColumns": "105,110", "endOffsets": "2763,2874"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\047aa7ddff6241681155a5178fa0e58d\\transformed\\preference-1.2.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,171,252,322,442,610,690", "endColumns": "65,80,69,119,167,79,76", "endOffsets": "166,247,317,437,605,685,762"}, "to": {"startLines": "56,58,62,63,66,67,68", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5357,5506,5873,5943,6242,6410,6490", "endColumns": "65,80,69,119,167,79,76", "endOffsets": "5418,5582,5938,6058,6405,6485,6562"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6ddfe7bc5a026c54878de2bc1391ef46\\transformed\\appcompat-1.1.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,295,395,477,574,680,757,833,924,1016,1113,1209,1304,1397,1492,1584,1675,1766,1843,1939,2034,2129,2226,2322,2420,2568,2662", "endColumns": "94,94,99,81,96,105,76,75,90,91,96,95,94,92,94,91,90,90,76,95,94,94,96,95,97,147,93,77", "endOffsets": "195,290,390,472,569,675,752,828,919,1011,1108,1204,1299,1392,1487,1579,1670,1761,1838,1934,2029,2124,2221,2317,2415,2563,2657,2735"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,295,395,477,574,680,757,833,924,1016,1113,1209,1304,1397,1492,1584,1675,1766,1843,1939,2034,2129,2226,2322,2420,2568,6063", "endColumns": "94,94,99,81,96,105,76,75,90,91,96,95,94,92,94,91,90,90,76,95,94,94,96,95,97,147,93,77", "endOffsets": "195,290,390,472,569,675,752,828,919,1011,1108,1204,1299,1392,1487,1579,1670,1761,1838,1934,2029,2124,2221,2317,2415,2563,2657,6136"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\889ae3adf7a24645889ee22f4dad2cac\\transformed\\core-1.13.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,248,342,436,529,623,719", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "142,243,337,431,524,618,714,815"}, "to": {"startLines": "31,32,33,34,35,36,37,65", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2879,2971,3072,3166,3260,3353,3447,6141", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "2966,3067,3161,3255,3348,3442,3538,6237"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6fe737438c032de0dc69b7164d4c8461\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-zh-rCN\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "94", "endOffsets": "293"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4405", "endColumns": "98", "endOffsets": "4499"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5721d2dca1c20b34574c347f20f3254f\\transformed\\jetified-play-services-base-18.5.0\\res\\values-zh-rCN\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,419,530,628,729,841,939,1027,1131,1228,1354,1465,1565,1669,1721,1774", "endColumns": "96,124,110,97,100,111,97,87,103,96,125,110,99,103,51,52,69", "endOffsets": "293,418,529,627,728,840,938,1026,1130,1227,1353,1464,1564,1668,1720,1773,1843"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3543,3644,3773,3888,3990,4095,4211,4313,4504,4612,4713,4843,4958,5062,5170,5226,5283", "endColumns": "100,128,114,101,104,115,101,91,107,100,129,114,103,107,55,56,73", "endOffsets": "3639,3768,3883,3985,4090,4206,4308,4400,4607,4708,4838,4953,5057,5165,5221,5278,5352"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7f6d20225ac1174a3efad96609161b97\\transformed\\browser-1.8.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,138,230,331", "endColumns": "82,91,100,92", "endOffsets": "133,225,326,419"}, "to": {"startLines": "57,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "5423,5587,5679,5780", "endColumns": "82,91,100,92", "endOffsets": "5501,5674,5775,5868"}}]}]}