[{"merged": "io.flutter.plugins.imagepicker.image_picker_android-merged_res-27:/drawable/notification_bg.xml", "source": "io.flutter.plugins.imagepicker.image_picker_android-core-1.13.1-9:/drawable/notification_bg.xml"}, {"merged": "io.flutter.plugins.imagepicker.image_picker_android-merged_res-27:/drawable/notification_bg_low.xml", "source": "io.flutter.plugins.imagepicker.image_picker_android-core-1.13.1-9:/drawable/notification_bg_low.xml"}, {"merged": "io.flutter.plugins.imagepicker.image_picker_android-merged_res-27:/drawable/notification_tile_bg.xml", "source": "io.flutter.plugins.imagepicker.image_picker_android-core-1.13.1-9:/drawable/notification_tile_bg.xml"}, {"merged": "io.flutter.plugins.imagepicker.image_picker_android-merged_res-27:/drawable/notification_icon_background.xml", "source": "io.flutter.plugins.imagepicker.image_picker_android-core-1.13.1-9:/drawable/notification_icon_background.xml"}]