1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.hotelvendor.management"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->E:\Ongoing\lib\mobile-apps\hotel-vendor-app\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->E:\Ongoing\lib\mobile-apps\hotel-vendor-app\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->E:\Ongoing\lib\mobile-apps\hotel-vendor-app\android\app\src\main\AndroidManifest.xml:39:5-44:15
24        <intent>
24-->E:\Ongoing\lib\mobile-apps\hotel-vendor-app\android\app\src\main\AndroidManifest.xml:40:9-43:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->E:\Ongoing\lib\mobile-apps\hotel-vendor-app\android\app\src\main\AndroidManifest.xml:41:13-72
25-->E:\Ongoing\lib\mobile-apps\hotel-vendor-app\android\app\src\main\AndroidManifest.xml:41:21-70
26
27            <data android:mimeType="text/plain" />
27-->E:\Ongoing\lib\mobile-apps\hotel-vendor-app\android\app\src\main\AndroidManifest.xml:42:13-50
27-->E:\Ongoing\lib\mobile-apps\hotel-vendor-app\android\app\src\main\AndroidManifest.xml:42:19-48
28        </intent>
29        <intent>
29-->[:file_picker] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:18
30            <action android:name="android.intent.action.GET_CONTENT" />
30-->[:file_picker] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-72
30-->[:file_picker] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:9:21-69
31
32            <data android:mimeType="*/*" />
32-->E:\Ongoing\lib\mobile-apps\hotel-vendor-app\android\app\src\main\AndroidManifest.xml:42:13-50
32-->E:\Ongoing\lib\mobile-apps\hotel-vendor-app\android\app\src\main\AndroidManifest.xml:42:19-48
33        </intent>
34    </queries>
35
36    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
36-->[:connectivity_plus] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\connectivity_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-79
36-->[:connectivity_plus] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\connectivity_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:7:22-76
37    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Permissions options for the `notification` group -->
37-->[:firebase_analytics] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_analytics\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-68
37-->[:firebase_analytics] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_analytics\intermediates\merged_manifest\debug\AndroidManifest.xml:9:22-65
38    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Required by older versions of Google Play services to create IID tokens -->
38-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-77
38-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:11:22-74
39    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
39-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\b75b0bc0a35aca6c9e40ab5eafc895e3\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:26:5-82
39-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\b75b0bc0a35aca6c9e40ab5eafc895e3\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:26:22-79
40    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
40-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\fd203cf27771660e78b7ff244baf0de3\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:25:5-79
40-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\fd203cf27771660e78b7ff244baf0de3\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:25:22-76
41    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
41-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\fd203cf27771660e78b7ff244baf0de3\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:26:5-88
41-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\fd203cf27771660e78b7ff244baf0de3\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:26:22-85
42    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
42-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\fd203cf27771660e78b7ff244baf0de3\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:27:5-82
42-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\fd203cf27771660e78b7ff244baf0de3\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:27:22-79
43    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
43-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29fad307f7078946d9f5420198c351d4\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:26:5-110
43-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29fad307f7078946d9f5420198c351d4\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:26:22-107
44    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
44-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\63c1826e9e9501705b709f1fed36b04e\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:5-98
44-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\63c1826e9e9501705b709f1fed36b04e\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:22-95
45
46    <permission
46-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\59cf662b2b8d845940ff382134f3256f\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
47        android:name="com.hotelvendor.management.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\59cf662b2b8d845940ff382134f3256f\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
48        android:protectionLevel="signature" />
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\59cf662b2b8d845940ff382134f3256f\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
49
50    <uses-permission android:name="com.hotelvendor.management.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
50-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\59cf662b2b8d845940ff382134f3256f\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
50-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\59cf662b2b8d845940ff382134f3256f\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
51
52    <application
53        android:name="android.app.Application"
54        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
54-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\59cf662b2b8d845940ff382134f3256f\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
55        android:debuggable="true"
56        android:extractNativeLibs="false"
57        android:icon="@mipmap/launcher_icon"
58        android:label="Vendor" >
59        <activity
60            android:name="com.example.hotel_vendor_app.MainActivity"
61            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
62            android:exported="true"
63            android:hardwareAccelerated="true"
64            android:launchMode="singleTop"
65            android:taskAffinity=""
66            android:theme="@style/LaunchTheme"
67            android:windowSoftInputMode="adjustResize" >
68
69            <!--
70                 Specifies an Android theme to apply to this Activity as soon as
71                 the Android process has started. This theme is visible to the user
72                 while the Flutter UI initializes. After that, this theme continues
73                 to determine the Window background behind the Flutter UI.
74            -->
75            <meta-data
76                android:name="io.flutter.embedding.android.NormalTheme"
77                android:resource="@style/NormalTheme" />
78
79            <intent-filter>
80                <action android:name="android.intent.action.MAIN" />
81
82                <category android:name="android.intent.category.LAUNCHER" />
83            </intent-filter>
84        </activity>
85        <!--
86             Don't delete the meta-data below.
87             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
88        -->
89        <meta-data
90            android:name="flutterEmbedding"
91            android:value="2" />
92
93        <service
93-->[:cloud_firestore] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
94            android:name="com.google.firebase.components.ComponentDiscoveryService"
94-->[:cloud_firestore] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:8:18-89
95            android:directBootAware="true"
95-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\86cb4f6cf8e876638da277ab0ef30e66\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
96            android:exported="false" >
96-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a29326dd00321d134d2e20eaffefc02a\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:68:13-37
97            <meta-data
97-->[:cloud_firestore] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
98                android:name="com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar"
98-->[:cloud_firestore] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-134
99                android:value="com.google.firebase.components.ComponentRegistrar" />
99-->[:cloud_firestore] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
100            <meta-data
100-->[:firebase_analytics] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_analytics\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-15:85
101                android:name="com.google.firebase.components:io.flutter.plugins.firebase.analytics.FlutterFirebaseAppRegistrar"
101-->[:firebase_analytics] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_analytics\intermediates\merged_manifest\debug\AndroidManifest.xml:14:17-128
102                android:value="com.google.firebase.components.ComponentRegistrar" />
102-->[:firebase_analytics] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_analytics\intermediates\merged_manifest\debug\AndroidManifest.xml:15:17-82
103            <meta-data
103-->[:firebase_auth] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
104                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
104-->[:firebase_auth] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-124
105                android:value="com.google.firebase.components.ComponentRegistrar" />
105-->[:firebase_auth] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
106            <meta-data
106-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:36:13-38:85
107                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
107-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:37:17-128
108                android:value="com.google.firebase.components.ComponentRegistrar" />
108-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:38:17-82
109            <meta-data
109-->[:firebase_storage] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_storage\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
110                android:name="com.google.firebase.components:io.flutter.plugins.firebase.storage.FlutterFirebaseAppRegistrar"
110-->[:firebase_storage] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_storage\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-126
111                android:value="com.google.firebase.components.ComponentRegistrar" />
111-->[:firebase_storage] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_storage\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
112            <meta-data
112-->[:firebase_core] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
113                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
113-->[:firebase_core] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-124
114                android:value="com.google.firebase.components.ComponentRegistrar" />
114-->[:firebase_core] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
115            <meta-data
115-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a29326dd00321d134d2e20eaffefc02a\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
116                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
116-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a29326dd00321d134d2e20eaffefc02a\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
117                android:value="com.google.firebase.components.ComponentRegistrar" />
117-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a29326dd00321d134d2e20eaffefc02a\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
118            <meta-data
118-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\903bb9d0fd2558e7884e1495f11fb022\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:17:13-19:85
119                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
119-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\903bb9d0fd2558e7884e1495f11fb022\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:18:17-122
120                android:value="com.google.firebase.components.ComponentRegistrar" />
120-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\903bb9d0fd2558e7884e1495f11fb022\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:19:17-82
121            <meta-data
121-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\903bb9d0fd2558e7884e1495f11fb022\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:20:13-22:85
122                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
122-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\903bb9d0fd2558e7884e1495f11fb022\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:21:17-111
123                android:value="com.google.firebase.components.ComponentRegistrar" />
123-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\903bb9d0fd2558e7884e1495f11fb022\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:22:17-82
124            <meta-data
124-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\b75b0bc0a35aca6c9e40ab5eafc895e3\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:57:13-59:85
125                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
125-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\b75b0bc0a35aca6c9e40ab5eafc895e3\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:58:17-122
126                android:value="com.google.firebase.components.ComponentRegistrar" />
126-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\b75b0bc0a35aca6c9e40ab5eafc895e3\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:59:17-82
127            <meta-data
127-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\b75b0bc0a35aca6c9e40ab5eafc895e3\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:60:13-62:85
128                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
128-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\b75b0bc0a35aca6c9e40ab5eafc895e3\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:61:17-119
129                android:value="com.google.firebase.components.ComponentRegistrar" />
129-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\b75b0bc0a35aca6c9e40ab5eafc895e3\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:62:17-82
130            <meta-data
130-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\e9f2d95eaa7afaff4f7e7e743aac1ea2\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:30:13-32:85
131                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
131-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\e9f2d95eaa7afaff4f7e7e743aac1ea2\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:31:17-118
132                android:value="com.google.firebase.components.ComponentRegistrar" />
132-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\e9f2d95eaa7afaff4f7e7e743aac1ea2\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:32:17-82
133            <meta-data
133-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\e9f2d95eaa7afaff4f7e7e743aac1ea2\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:33:13-35:85
134                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
134-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\e9f2d95eaa7afaff4f7e7e743aac1ea2\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:34:17-107
135                android:value="com.google.firebase.components.ComponentRegistrar" />
135-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\e9f2d95eaa7afaff4f7e7e743aac1ea2\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:35:17-82
136            <meta-data
136-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\fd203cf27771660e78b7ff244baf0de3\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:33:13-35:85
137                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
137-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\fd203cf27771660e78b7ff244baf0de3\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:34:17-139
138                android:value="com.google.firebase.components.ComponentRegistrar" />
138-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\fd203cf27771660e78b7ff244baf0de3\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:35:17-82
139            <meta-data
139-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\899f616fa572ba5f7744bae3326ca752\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
140                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
140-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\899f616fa572ba5f7744bae3326ca752\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
141                android:value="com.google.firebase.components.ComponentRegistrar" />
141-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\899f616fa572ba5f7744bae3326ca752\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
142            <meta-data
142-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\899f616fa572ba5f7744bae3326ca752\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
143                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
143-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\899f616fa572ba5f7744bae3326ca752\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
144                android:value="com.google.firebase.components.ComponentRegistrar" />
144-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\899f616fa572ba5f7744bae3326ca752\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
145            <meta-data
145-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\32e33a5a9fb8eb2c5e05b9a33d4394ac\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
146                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
146-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\32e33a5a9fb8eb2c5e05b9a33d4394ac\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
147                android:value="com.google.firebase.components.ComponentRegistrar" />
147-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\32e33a5a9fb8eb2c5e05b9a33d4394ac\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
148            <meta-data
148-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\32e33a5a9fb8eb2c5e05b9a33d4394ac\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
149                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
149-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\32e33a5a9fb8eb2c5e05b9a33d4394ac\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
150                android:value="com.google.firebase.components.ComponentRegistrar" />
150-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\32e33a5a9fb8eb2c5e05b9a33d4394ac\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
151            <meta-data
151-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3afbbde8053677b90af1f801495b8d5\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
152                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
152-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3afbbde8053677b90af1f801495b8d5\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
153                android:value="com.google.firebase.components.ComponentRegistrar" />
153-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3afbbde8053677b90af1f801495b8d5\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
154            <meta-data
154-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\86cb4f6cf8e876638da277ab0ef30e66\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
155                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
155-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\86cb4f6cf8e876638da277ab0ef30e66\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
156                android:value="com.google.firebase.components.ComponentRegistrar" />
156-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\86cb4f6cf8e876638da277ab0ef30e66\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
157            <meta-data
157-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\e05cb0ca27341d585ff51dff26762463\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
158                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
158-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\e05cb0ca27341d585ff51dff26762463\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
159                android:value="com.google.firebase.components.ComponentRegistrar" />
159-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\e05cb0ca27341d585ff51dff26762463\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
160        </service>
161        <service
161-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:14:9-17:72
162            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
162-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-107
163            android:exported="false"
163-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-37
164            android:permission="android.permission.BIND_JOB_SERVICE" />
164-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-69
165        <service
165-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:18:9-24:19
166            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
166-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:19:13-97
167            android:exported="false" >
167-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:20:13-37
168            <intent-filter>
168-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-23:29
169                <action android:name="com.google.firebase.MESSAGING_EVENT" />
169-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:22:17-78
169-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:22:25-75
170            </intent-filter>
171        </service>
172
173        <receiver
173-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:26:9-33:20
174            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
174-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:27:13-98
175            android:exported="true"
175-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-36
176            android:permission="com.google.android.c2dm.permission.SEND" >
176-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:29:13-73
177            <intent-filter>
177-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-32:29
178                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
178-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:17-81
178-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:25-78
179            </intent-filter>
180        </receiver>
181
182        <provider
182-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:41:9-45:38
183            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
183-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:42:13-102
184            android:authorities="com.hotelvendor.management.flutterfirebasemessaginginitprovider"
184-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:43:13-88
185            android:exported="false"
185-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:44:13-37
186            android:initOrder="99" />
186-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:45:13-35
187        <provider
187-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:9-17:20
188            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
188-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-82
189            android:authorities="com.hotelvendor.management.flutter.image_provider"
189-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-74
190            android:exported="false"
190-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-37
191            android:grantUriPermissions="true" >
191-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-47
192            <meta-data
192-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-16:75
193                android:name="android.support.FILE_PROVIDER_PATHS"
193-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:15:17-67
194                android:resource="@xml/flutter_image_picker_file_paths" />
194-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:16:17-72
195        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
196        <service
196-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:19:9-31:19
197            android:name="com.google.android.gms.metadata.ModuleDependencies"
197-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:20:13-78
198            android:enabled="false"
198-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-36
199            android:exported="false" >
199-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-37
200            <intent-filter>
200-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:24:13-26:29
201                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
201-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:25:17-94
201-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:25:25-91
202            </intent-filter>
203
204            <meta-data
204-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-30:36
205                android:name="photopicker_activity:0:required"
205-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:29:17-63
206                android:value="" />
206-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:30:17-33
207        </service>
208        <service
208-->[:geolocator_android] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:56
209            android:name="com.baseflow.geolocator.GeolocatorLocationService"
209-->[:geolocator_android] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-77
210            android:enabled="true"
210-->[:geolocator_android] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-35
211            android:exported="false"
211-->[:geolocator_android] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-37
212            android:foregroundServiceType="location" />
212-->[:geolocator_android] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-53
213
214        <activity
214-->[:url_launcher_android] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-11:74
215            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
215-->[:url_launcher_android] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-74
216            android:exported="false"
216-->[:url_launcher_android] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-37
217            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
217-->[:url_launcher_android] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-71
218        <activity
218-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a29326dd00321d134d2e20eaffefc02a\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
219            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
219-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a29326dd00321d134d2e20eaffefc02a\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
220            android:excludeFromRecents="true"
220-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a29326dd00321d134d2e20eaffefc02a\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
221            android:exported="true"
221-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a29326dd00321d134d2e20eaffefc02a\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
222            android:launchMode="singleTask"
222-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a29326dd00321d134d2e20eaffefc02a\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
223            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
223-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a29326dd00321d134d2e20eaffefc02a\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
224            <intent-filter>
224-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a29326dd00321d134d2e20eaffefc02a\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
225                <action android:name="android.intent.action.VIEW" />
225-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a29326dd00321d134d2e20eaffefc02a\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
225-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a29326dd00321d134d2e20eaffefc02a\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
226
227                <category android:name="android.intent.category.DEFAULT" />
227-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a29326dd00321d134d2e20eaffefc02a\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
227-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a29326dd00321d134d2e20eaffefc02a\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
228                <category android:name="android.intent.category.BROWSABLE" />
228-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a29326dd00321d134d2e20eaffefc02a\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
228-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a29326dd00321d134d2e20eaffefc02a\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
229
230                <data
230-->E:\Ongoing\lib\mobile-apps\hotel-vendor-app\android\app\src\main\AndroidManifest.xml:42:13-50
231                    android:host="firebase.auth"
232                    android:path="/"
233                    android:scheme="genericidp" />
234            </intent-filter>
235        </activity>
236        <activity
236-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a29326dd00321d134d2e20eaffefc02a\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
237            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
237-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a29326dd00321d134d2e20eaffefc02a\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
238            android:excludeFromRecents="true"
238-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a29326dd00321d134d2e20eaffefc02a\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
239            android:exported="true"
239-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a29326dd00321d134d2e20eaffefc02a\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
240            android:launchMode="singleTask"
240-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a29326dd00321d134d2e20eaffefc02a\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
241            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
241-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a29326dd00321d134d2e20eaffefc02a\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
242            <intent-filter>
242-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a29326dd00321d134d2e20eaffefc02a\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
243                <action android:name="android.intent.action.VIEW" />
243-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a29326dd00321d134d2e20eaffefc02a\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
243-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a29326dd00321d134d2e20eaffefc02a\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
244
245                <category android:name="android.intent.category.DEFAULT" />
245-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a29326dd00321d134d2e20eaffefc02a\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
245-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a29326dd00321d134d2e20eaffefc02a\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
246                <category android:name="android.intent.category.BROWSABLE" />
246-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a29326dd00321d134d2e20eaffefc02a\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
246-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a29326dd00321d134d2e20eaffefc02a\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
247
248                <data
248-->E:\Ongoing\lib\mobile-apps\hotel-vendor-app\android\app\src\main\AndroidManifest.xml:42:13-50
249                    android:host="firebase.auth"
250                    android:path="/"
251                    android:scheme="recaptcha" />
252            </intent-filter>
253        </activity>
254
255        <uses-library
255-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8b7c6d211140d08bab99021d62acaf57\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
256            android:name="androidx.window.extensions"
256-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8b7c6d211140d08bab99021d62acaf57\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
257            android:required="false" />
257-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8b7c6d211140d08bab99021d62acaf57\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
258        <uses-library
258-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8b7c6d211140d08bab99021d62acaf57\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
259            android:name="androidx.window.sidecar"
259-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8b7c6d211140d08bab99021d62acaf57\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
260            android:required="false" />
260-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8b7c6d211140d08bab99021d62acaf57\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
261
262        <service
262-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\2ed3d9ef39026d9eb9bb9638db04c622\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
263            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
263-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\2ed3d9ef39026d9eb9bb9638db04c622\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
264            android:enabled="true"
264-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\2ed3d9ef39026d9eb9bb9638db04c622\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
265            android:exported="false" >
265-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\2ed3d9ef39026d9eb9bb9638db04c622\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
266            <meta-data
266-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\2ed3d9ef39026d9eb9bb9638db04c622\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
267                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
267-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\2ed3d9ef39026d9eb9bb9638db04c622\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
268                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
268-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\2ed3d9ef39026d9eb9bb9638db04c622\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
269        </service>
270
271        <activity
271-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\2ed3d9ef39026d9eb9bb9638db04c622\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
272            android:name="androidx.credentials.playservices.HiddenActivity"
272-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\2ed3d9ef39026d9eb9bb9638db04c622\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
273            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
273-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\2ed3d9ef39026d9eb9bb9638db04c622\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
274            android:enabled="true"
274-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\2ed3d9ef39026d9eb9bb9638db04c622\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
275            android:exported="false"
275-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\2ed3d9ef39026d9eb9bb9638db04c622\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
276            android:fitsSystemWindows="true"
276-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\2ed3d9ef39026d9eb9bb9638db04c622\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
277            android:theme="@style/Theme.Hidden" >
277-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\2ed3d9ef39026d9eb9bb9638db04c622\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
278        </activity>
279        <activity
279-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ef3a4bf6475dbdbe4cc428deddc76cec\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
280            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
280-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ef3a4bf6475dbdbe4cc428deddc76cec\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
281            android:excludeFromRecents="true"
281-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ef3a4bf6475dbdbe4cc428deddc76cec\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
282            android:exported="false"
282-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ef3a4bf6475dbdbe4cc428deddc76cec\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
283            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
283-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ef3a4bf6475dbdbe4cc428deddc76cec\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
284        <!--
285            Service handling Google Sign-In user revocation. For apps that do not integrate with
286            Google Sign-In, this service will never be started.
287        -->
288        <service
288-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ef3a4bf6475dbdbe4cc428deddc76cec\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
289            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
289-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ef3a4bf6475dbdbe4cc428deddc76cec\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
290            android:exported="true"
290-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ef3a4bf6475dbdbe4cc428deddc76cec\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
291            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
291-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ef3a4bf6475dbdbe4cc428deddc76cec\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
292            android:visibleToInstantApps="true" />
292-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ef3a4bf6475dbdbe4cc428deddc76cec\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
293
294        <receiver
294-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\b75b0bc0a35aca6c9e40ab5eafc895e3\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:29:9-40:20
295            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
295-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\b75b0bc0a35aca6c9e40ab5eafc895e3\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:30:13-78
296            android:exported="true"
296-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\b75b0bc0a35aca6c9e40ab5eafc895e3\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:31:13-36
297            android:permission="com.google.android.c2dm.permission.SEND" >
297-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\b75b0bc0a35aca6c9e40ab5eafc895e3\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:32:13-73
298            <intent-filter>
298-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-32:29
299                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
299-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:17-81
299-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:31:25-78
300            </intent-filter>
301
302            <meta-data
302-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\b75b0bc0a35aca6c9e40ab5eafc895e3\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:37:13-39:40
303                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
303-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\b75b0bc0a35aca6c9e40ab5eafc895e3\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:38:17-92
304                android:value="true" />
304-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\b75b0bc0a35aca6c9e40ab5eafc895e3\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:39:17-37
305        </receiver>
306        <!--
307             FirebaseMessagingService performs security checks at runtime,
308             but set to not exported to explicitly avoid allowing another app to call it.
309        -->
310        <service
310-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\b75b0bc0a35aca6c9e40ab5eafc895e3\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:46:9-53:19
311            android:name="com.google.firebase.messaging.FirebaseMessagingService"
311-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\b75b0bc0a35aca6c9e40ab5eafc895e3\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:47:13-82
312            android:directBootAware="true"
312-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\b75b0bc0a35aca6c9e40ab5eafc895e3\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:48:13-43
313            android:exported="false" >
313-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\b75b0bc0a35aca6c9e40ab5eafc895e3\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:49:13-37
314            <intent-filter android:priority="-500" >
314-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-23:29
315                <action android:name="com.google.firebase.MESSAGING_EVENT" />
315-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:22:17-78
315-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\AndroidManifest.xml:22:25-75
316            </intent-filter>
317        </service>
318
319        <provider
319-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\86cb4f6cf8e876638da277ab0ef30e66\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
320            android:name="com.google.firebase.provider.FirebaseInitProvider"
320-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\86cb4f6cf8e876638da277ab0ef30e66\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
321            android:authorities="com.hotelvendor.management.firebaseinitprovider"
321-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\86cb4f6cf8e876638da277ab0ef30e66\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
322            android:directBootAware="true"
322-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\86cb4f6cf8e876638da277ab0ef30e66\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
323            android:exported="false"
323-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\86cb4f6cf8e876638da277ab0ef30e66\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
324            android:initOrder="100" />
324-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\86cb4f6cf8e876638da277ab0ef30e66\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
325
326        <receiver
326-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29fad307f7078946d9f5420198c351d4\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:29:9-33:20
327            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
327-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29fad307f7078946d9f5420198c351d4\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:30:13-85
328            android:enabled="true"
328-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29fad307f7078946d9f5420198c351d4\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:31:13-35
329            android:exported="false" >
329-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29fad307f7078946d9f5420198c351d4\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:32:13-37
330        </receiver>
331
332        <service
332-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29fad307f7078946d9f5420198c351d4\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:35:9-38:40
333            android:name="com.google.android.gms.measurement.AppMeasurementService"
333-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29fad307f7078946d9f5420198c351d4\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:36:13-84
334            android:enabled="true"
334-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29fad307f7078946d9f5420198c351d4\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:37:13-35
335            android:exported="false" />
335-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29fad307f7078946d9f5420198c351d4\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:38:13-37
336        <service
336-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29fad307f7078946d9f5420198c351d4\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:39:9-43:72
337            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
337-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29fad307f7078946d9f5420198c351d4\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:40:13-87
338            android:enabled="true"
338-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29fad307f7078946d9f5420198c351d4\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:41:13-35
339            android:exported="false"
339-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29fad307f7078946d9f5420198c351d4\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:42:13-37
340            android:permission="android.permission.BIND_JOB_SERVICE" />
340-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29fad307f7078946d9f5420198c351d4\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:43:13-69
341
342        <provider
342-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\496b7496257452f4dfdc067d33e1b491\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
343            android:name="androidx.startup.InitializationProvider"
343-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\496b7496257452f4dfdc067d33e1b491\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
344            android:authorities="com.hotelvendor.management.androidx-startup"
344-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\496b7496257452f4dfdc067d33e1b491\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
345            android:exported="false" >
345-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\496b7496257452f4dfdc067d33e1b491\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
346            <meta-data
346-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\496b7496257452f4dfdc067d33e1b491\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
347                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
347-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\496b7496257452f4dfdc067d33e1b491\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
348                android:value="androidx.startup" />
348-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\496b7496257452f4dfdc067d33e1b491\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
349            <meta-data
349-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0a48c25826e068bb0e2ed37de506972\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
350                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
350-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0a48c25826e068bb0e2ed37de506972\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
351                android:value="androidx.startup" />
351-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0a48c25826e068bb0e2ed37de506972\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
352        </provider>
353
354        <uses-library
354-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\transforms-3\bfcd9c1af845cc8b80afc62b0bb271a4\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
355            android:name="android.ext.adservices"
355-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\transforms-3\bfcd9c1af845cc8b80afc62b0bb271a4\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
356            android:required="false" />
356-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\transforms-3\bfcd9c1af845cc8b80afc62b0bb271a4\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
357
358        <activity
358-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\a0aa6191d710310d82f16cc8f8088c89\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
359            android:name="com.google.android.gms.common.api.GoogleApiActivity"
359-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\a0aa6191d710310d82f16cc8f8088c89\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
360            android:exported="false"
360-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\a0aa6191d710310d82f16cc8f8088c89\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
361            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
361-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\a0aa6191d710310d82f16cc8f8088c89\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
362
363        <meta-data
363-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cc8b6019d92a5e15cae9db0f9def0ab\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
364            android:name="com.google.android.gms.version"
364-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cc8b6019d92a5e15cae9db0f9def0ab\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
365            android:value="@integer/google_play_services_version" />
365-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cc8b6019d92a5e15cae9db0f9def0ab\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
366
367        <receiver
367-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0a48c25826e068bb0e2ed37de506972\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
368            android:name="androidx.profileinstaller.ProfileInstallReceiver"
368-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0a48c25826e068bb0e2ed37de506972\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
369            android:directBootAware="false"
369-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0a48c25826e068bb0e2ed37de506972\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
370            android:enabled="true"
370-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0a48c25826e068bb0e2ed37de506972\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
371            android:exported="true"
371-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0a48c25826e068bb0e2ed37de506972\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
372            android:permission="android.permission.DUMP" >
372-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0a48c25826e068bb0e2ed37de506972\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
373            <intent-filter>
373-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0a48c25826e068bb0e2ed37de506972\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
374                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
374-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0a48c25826e068bb0e2ed37de506972\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
374-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0a48c25826e068bb0e2ed37de506972\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
375            </intent-filter>
376            <intent-filter>
376-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0a48c25826e068bb0e2ed37de506972\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
377                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
377-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0a48c25826e068bb0e2ed37de506972\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
377-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0a48c25826e068bb0e2ed37de506972\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
378            </intent-filter>
379            <intent-filter>
379-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0a48c25826e068bb0e2ed37de506972\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
380                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
380-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0a48c25826e068bb0e2ed37de506972\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
380-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0a48c25826e068bb0e2ed37de506972\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
381            </intent-filter>
382            <intent-filter>
382-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0a48c25826e068bb0e2ed37de506972\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
383                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
383-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0a48c25826e068bb0e2ed37de506972\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
383-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\d0a48c25826e068bb0e2ed37de506972\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
384            </intent-filter>
385        </receiver>
386
387        <service
387-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\bb92062c51107c84c63d9cb5941de844\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
388            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
388-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\bb92062c51107c84c63d9cb5941de844\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
389            android:exported="false" >
389-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\bb92062c51107c84c63d9cb5941de844\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
390            <meta-data
390-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\bb92062c51107c84c63d9cb5941de844\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
391                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
391-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\bb92062c51107c84c63d9cb5941de844\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
392                android:value="cct" />
392-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\bb92062c51107c84c63d9cb5941de844\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
393        </service>
394        <service
394-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\350b1c7cc1af5956f048d86c20fdec2a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
395            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
395-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\350b1c7cc1af5956f048d86c20fdec2a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
396            android:exported="false"
396-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\350b1c7cc1af5956f048d86c20fdec2a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
397            android:permission="android.permission.BIND_JOB_SERVICE" >
397-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\350b1c7cc1af5956f048d86c20fdec2a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
398        </service>
399
400        <receiver
400-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\350b1c7cc1af5956f048d86c20fdec2a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
401            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
401-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\350b1c7cc1af5956f048d86c20fdec2a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
402            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
402-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\350b1c7cc1af5956f048d86c20fdec2a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
403        <activity
403-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\e15cab99671a1cbe244cc623f2c5226d\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
404            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
404-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\e15cab99671a1cbe244cc623f2c5226d\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
405            android:exported="false"
405-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\e15cab99671a1cbe244cc623f2c5226d\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
406            android:stateNotNeeded="true"
406-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\e15cab99671a1cbe244cc623f2c5226d\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
407            android:theme="@style/Theme.PlayCore.Transparent" />
407-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\e15cab99671a1cbe244cc623f2c5226d\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
408    </application>
409
410</manifest>
