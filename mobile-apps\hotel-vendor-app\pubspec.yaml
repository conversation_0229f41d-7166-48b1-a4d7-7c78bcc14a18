name: vendor
description: "Vendor - Professional Hotel Management System with Firebase Integration, Aadhar Verification, and Real-time Booking Management"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.6.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # UI and Icons
  cupertino_icons: ^1.0.8
  material_design_icons_flutter: ^7.0.7296

  # Firebase
  firebase_core: ^3.6.0
  firebase_auth: ^5.3.1
  cloud_firestore: ^5.4.3
  firebase_storage: ^12.3.2
  firebase_messaging: ^15.1.3
  firebase_analytics: ^11.3.3

  # Authentication
  google_sign_in: ^6.2.1

  # State Management
  provider: ^6.1.2

  # HTTP and API
  http: ^1.2.2
  dio: ^5.7.0

  # Image Handling
  image_picker: ^1.1.2
  cached_network_image: ^3.4.1
  flutter_svg: ^2.0.10+1

  # Local Storage
  shared_preferences: ^2.3.2

  # Date and Time
  intl: ^0.19.0

  # Location
  geolocator: ^13.0.1
  geocoding: ^3.0.0

  # Hotel API Integration (will implement custom Amadeus integration)

  # UI Components
  flutter_rating_bar: ^4.0.1
  carousel_slider: ^5.0.0
  shimmer: ^3.0.0
  percent_indicator: ^4.2.3
  calendar_date_picker2: ^1.1.7

  # Utilities
  url_launcher: ^6.3.1
  permission_handler: ^11.3.1
  connectivity_plus: ^6.0.5
  package_info_plus: ^8.0.2

  # File handling
  file_picker: ^8.1.2
  path_provider: ^2.1.4

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.14.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/logos/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

# Flutter Launcher Icons Configuration
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/logos/link_in_blink_black.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  web:
    generate: true
    image_path: "assets/logos/link_in_blink_black.png"
    background_color: "#hexcode"
    theme_color: "#hexcode"
  windows:
    generate: true
    image_path: "assets/logos/link_in_blink_black.png"
    icon_size: 48 # min:48, max:256, default: 48
