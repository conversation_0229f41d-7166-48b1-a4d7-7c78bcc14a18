"-Xallow-no-source-files" "-classpath" "E:\\Ongoing\\lib\\mobile-apps\\hotel-vendor-app\\build\\shared_preferences_android\\intermediates\\compile_r_class_jar\\debug\\R.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\330de9d0269bc21c75ab4165827099b5\\transformed\\jetified-flutter_embedding_debug-1.0.0-cb4b5fff73850b2e42bd4de7cb9a4310a78ac40d.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\25e87b0414c025d14d5a03fc245cec3c\\transformed\\preference-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fbfdc28ccd6a2fbd7b2077a37599a45e\\transformed\\appcompat-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\528bea070c88dd94a6ba69488c74cc86\\transformed\\jetified-fragment-ktx-1.7.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\deb7f0f4abbc1df04bf2840c7cafbd0c\\transformed\\fragment-1.7.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\aa2909629bad3baa24c205b4f0d0806a\\transformed\\recyclerview-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\09783ba92c26349b2800067071d34472\\transformed\\jetified-activity-ktx-1.8.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\815c389afaec2b9e5ad6e818291cfb48\\transformed\\jetified-activity-1.8.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5ee5f30e40a7d5db26a4766c63839881\\transformed\\legacy-support-core-ui-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6d3a936cccd172a27b9c9de5dd55692a\\transformed\\legacy-support-core-utils-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\97522a3bf92b730870592d9637158bd6\\transformed\\loader-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\39d5013d04e956b223ab31eee593be92\\transformed\\lifecycle-livedata-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\eecf3a31c429d8a4700cd7e34061860f\\transformed\\jetified-lifecycle-livedata-core-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\491d8d0fa352b585fddd1ea1d6aa872f\\transformed\\jetified-lifecycle-viewmodel-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a40484898e5aa5bd5eae1db046455eb2\\transformed\\jetified-lifecycle-runtime-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9a3af9325cc77c030edb8295730db573\\transformed\\lifecycle-viewmodel-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfb7a4c6b5798c6306fa2bea9e16a797\\transformed\\lifecycle-livedata-core-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\84c905ba5d5b60ba7bdf9d5d74da33d6\\transformed\\jetified-lifecycle-viewmodel-savedstate-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b08e12880e40b0a05ccc1f0dd89c3ea1\\transformed\\jetified-core-ktx-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7a93e8bfe101c817fed1be9f36bb7f5b\\transformed\\viewpager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9c2ee75b46fb42c0ff481dfc64d1da0\\transformed\\jetified-appcompat-resources-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d5aab42f6319ba585902aebdd38ae996\\transformed\\drawerlayout-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\aad7e44987565c2e5de9ff2d6d65ee3e\\transformed\\slidingpanelayout-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfce8b38ad7a6745b10aaae1e5bf3953\\transformed\\coordinatorlayout-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\79793f041f216fe9890c3a127171127b\\transformed\\customview-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b49ea9dcc2d3ea7e5e03a6b282775d14\\transformed\\vectordrawable-animated-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\23aa6f339306a25f9139f012ddaa34cb\\transformed\\vectordrawable-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\48c2524b77f8e9a24c7d054f88adeea5\\transformed\\swiperefreshlayout-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\001e34acd0c9bb2fb12e149936b4bf6c\\transformed\\asynclayoutinflater-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2b1dfba3187fc43a949ed4cedf3fb3e7\\transformed\\core-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e1cd8d3d001bfb42ac91e3730ff60302\\transformed\\lifecycle-runtime-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9899a1644e2b6fd744e410b34e27a591\\transformed\\jetified-lifecycle-process-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common-java8\\2.7.0\\2ad14aed781c4a73ed4dbb421966d408a0a06686\\lifecycle-common-java8-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common\\2.7.0\\85334205d65cca70ed0109c3acbd29e22a2d9cb1\\lifecycle-common-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\78c82ed3b21105802501ef152a544907\\transformed\\jetified-window-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a73c2977716c5032d1eeda2aa3016efa\\transformed\\jetified-window-java-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\097b5a1af11f1a1bd41967bc491ace35\\transformed\\jetified-datastore-preferences-core-jvm-1.1.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\30359e4b977584de4336f73a94bdfe48\\transformed\\jetified-datastore-core-okio-jvm-1.1.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d5e2c6c3e04e1888af9e8003496a9e39\\transformed\\jetified-datastore-core-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e2d0c30e16df253db4319a805dc467ce\\transformed\\jetified-datastore-preferences-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\85bb19b8e5d1ed3d0a91bf5bd0915699\\transformed\\jetified-datastore-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6fab8d05e38287f76bcc48f3feb0863c\\transformed\\jetified-annotation-experimental-1.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6469f1aebf41ec25ee90b327d47cfd2b\\transformed\\jetified-savedstate-ktx-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\06eb1cb0a88080033ef3e31ca975dac4\\transformed\\jetified-savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c19d6aa8f5d8641bc446de295a2ac23d\\transformed\\core-runtime-2.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5021969e9457d543556a2900b54d7713\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2ee322a4be0dbe52ece354a153a29598\\transformed\\jetified-collection-ktx-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection\\1.1.0\\1f27220b47669781457de0d600849a5de0e89909\\collection-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ab651bb49676f70812773ae99de34585\\transformed\\cursoradapter-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b13cfead157be3e74d1a19a09c1d3f7b\\transformed\\interpolator-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc070b40f09894a5ca291bcac5bce89d\\transformed\\documentfile-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\71335eb04f03cf589e403916572a7994\\transformed\\localbroadcastmanager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fbb78c32032b9b2265ed47ad4b71d263\\transformed\\print-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\91bb7c3f2df8decb6703bbcd2576bae6\\transformed\\jetified-annotation-jvm-1.8.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ab7f49030ee21d25538e0e6945e4b88b\\transformed\\jetified-kotlinx-coroutines-android-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6db2c170614ff7814a2b897821838d8a\\transformed\\jetified-kotlinx-coroutines-core-jvm-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b10818950800b6c87dd50a180dabd1c6\\transformed\\jetified-okio-jvm-3.4.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0c779ca3ab47364db761a1b03154546a\\transformed\\jetified-kotlin-stdlib-jdk8-1.8.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\40041736908de81d08ee7369c4e86171\\transformed\\jetified-kotlin-stdlib-jdk7-1.8.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\305b928debbed68a5a926ab9611046a1\\transformed\\jetified-kotlin-stdlib-2.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2d0f4dd6ee9c4f99a5fbe26af0ab6db6\\transformed\\jetified-annotations-23.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9d2ac6228c83365ba8747ae5ec8a6af2\\transformed\\jetified-startup-runtime-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\55145276884fc491f4be99cac0578ae5\\transformed\\jetified-tracing-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b2baf1f54a5165bb17579eb4d0b6eeca\\transformed\\jetified-relinker-1.4.5-api.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\platforms\\android-35\\android.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\build-tools\\33.0.1\\core-lambda-stubs.jar" "-d" "E:\\Ongoing\\lib\\mobile-apps\\hotel-vendor-app\\build\\shared_preferences_android\\tmp\\kotlin-classes\\debug" "-jvm-target" "11" "-module-name" "shared_preferences_android_debug" "-no-jdk" "-no-reflect" "-no-stdlib" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\android\\src\\main\\java\\io\\flutter\\plugins\\sharedpreferences\\LegacySharedPreferencesPlugin.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\android\\src\\main\\java\\io\\flutter\\plugins\\sharedpreferences\\Messages.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\android\\src\\main\\java\\io\\flutter\\plugins\\sharedpreferences\\SharedPreferencesListEncoder.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\android\\src\\main\\kotlin\\io\\flutter\\plugins\\sharedpreferences\\MessagesAsync.g.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\android\\src\\main\\kotlin\\io\\flutter\\plugins\\sharedpreferences\\SharedPreferencesPlugin.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\android\\src\\main\\kotlin\\io\\flutter\\plugins\\sharedpreferences\\StringListObjectInputStream.kt"