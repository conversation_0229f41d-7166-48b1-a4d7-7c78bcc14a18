import 'package:flutter/foundation.dart';
import '../models/hotel_model.dart';
import '../services/amadeus_service.dart';
import '../services/vendor_integration_service.dart';

class HotelProvider with ChangeNotifier {
  final AmadeusService _amadeusService = AmadeusService();
  final VendorIntegrationService _vendorService = VendorIntegrationService();

  // State variables
  bool _isLoading = false;
  String? _errorMessage;
  List<HotelOffer> _hotels = [];
  HotelDetails? _selectedHotelDetails;
  HotelOffer? _selectedHotel;
  List<HotelBooking> _bookings = [];

  // Search parameters
  String _searchCity = '';
  DateTime? _checkInDate;
  DateTime? _checkOutDate;
  int _adults = 2;
  int _children = 0;
  int _rooms = 1;
  String _currency = 'INR';

  // Filters
  double _minPrice = 0;
  double _maxPrice = 50000;
  double _minRating = 0;
  List<String> _selectedAmenities = [];
  String _sortBy = 'price'; // price, rating, name

  // Getters
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  List<HotelOffer> get hotels => _hotels;
  HotelDetails? get selectedHotelDetails => _selectedHotelDetails;
  HotelOffer? get selectedHotel => _selectedHotel;
  List<HotelBooking> get bookings => _bookings;

  // Search parameters getters
  String get searchCity => _searchCity;
  DateTime? get checkInDate => _checkInDate;
  DateTime? get checkOutDate => _checkOutDate;
  int get adults => _adults;
  int get children => _children;
  int get rooms => _rooms;
  String get currency => _currency;

  // Filter getters
  double get minPrice => _minPrice;
  double get maxPrice => _maxPrice;
  double get minRating => _minRating;
  List<String> get selectedAmenities => _selectedAmenities;
  String get sortBy => _sortBy;

  // Computed getters
  List<HotelOffer> get filteredHotels {
    var filtered = _hotels.where((hotel) {
      // Price filter
      if (hotel.minPrice < _minPrice || hotel.minPrice > _maxPrice) {
        return false;
      }

      // Rating filter
      if (hotel.rating < _minRating) {
        return false;
      }

      // Amenities filter
      if (_selectedAmenities.isNotEmpty) {
        final hasRequiredAmenities = _selectedAmenities.every(
          (amenity) => hotel.amenities.contains(amenity),
        );
        if (!hasRequiredAmenities) {
          return false;
        }
      }

      return true;
    }).toList();

    // Sort hotels
    switch (_sortBy) {
      case 'price':
        filtered.sort((a, b) => a.minPrice.compareTo(b.minPrice));
        break;
      case 'rating':
        filtered.sort((a, b) => b.rating.compareTo(a.rating));
        break;
      case 'name':
        filtered.sort((a, b) => a.name.compareTo(b.name));
        break;
    }

    return filtered;
  }

  int? get numberOfNights {
    if (_checkInDate != null && _checkOutDate != null) {
      return _checkOutDate!.difference(_checkInDate!).inDays;
    }
    return null;
  }

  // Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Set error message
  void _setError(String? error) {
    _errorMessage = error;
    notifyListeners();
  }

  // Clear error
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Update search parameters
  void updateSearchParameters({
    String? city,
    DateTime? checkIn,
    DateTime? checkOut,
    int? adults,
    int? children,
    int? rooms,
    String? currency,
  }) {
    if (city != null) _searchCity = city;
    if (checkIn != null) _checkInDate = checkIn;
    if (checkOut != null) _checkOutDate = checkOut;
    if (adults != null) _adults = adults;
    if (children != null) _children = children;
    if (rooms != null) _rooms = rooms;
    if (currency != null) _currency = currency;
    notifyListeners();
  }

  // Update filters
  void updateFilters({
    double? minPrice,
    double? maxPrice,
    double? minRating,
    List<String>? amenities,
    String? sortBy,
  }) {
    if (minPrice != null) _minPrice = minPrice;
    if (maxPrice != null) _maxPrice = maxPrice;
    if (minRating != null) _minRating = minRating;
    if (amenities != null) _selectedAmenities = amenities;
    if (sortBy != null) _sortBy = sortBy;
    notifyListeners();
  }

  // Search hotels by city
  Future<void> searchHotels() async {
    if (_searchCity.isEmpty || _checkInDate == null || _checkOutDate == null) {
      _setError('Please fill in all search parameters');
      return;
    }

    _setLoading(true);
    _setError(null);

    try {
      // Get city code first
      final cityCode = await _amadeusService.getCityCode(_searchCity);
      if (cityCode == null) {
        _setError('City not found. Please try a different city name.');
        _setLoading(false);
        return;
      }

      // Search hotels
      final result = await _amadeusService.searchHotels(
        cityCode: cityCode,
        checkInDate: _checkInDate!,
        checkOutDate: _checkOutDate!,
        adults: _adults,
        children: _children,
        rooms: _rooms,
        currency: _currency,
        maxResults: 50,
      );

      if (result.isSuccess) {
        _hotels = result.hotels ?? [];
        if (_hotels.isEmpty) {
          _setError('No hotels found for the selected criteria');
        }
      } else {
        _setError(result.errorMessage ?? 'Failed to search hotels');
      }
    } catch (e) {
      _setError('An error occurred while searching hotels');
      if (kDebugMode) {
        print('Hotel search error: $e');
      }
    } finally {
      _setLoading(false);
    }
  }

  // Search hotels by location
  Future<void> searchHotelsByLocation({
    required double latitude,
    required double longitude,
    double radius = 5.0,
  }) async {
    if (_checkInDate == null || _checkOutDate == null) {
      _setError('Please select check-in and check-out dates');
      return;
    }

    _setLoading(true);
    _setError(null);

    try {
      final result = await _amadeusService.searchHotelsByLocation(
        latitude: latitude,
        longitude: longitude,
        radius: radius,
        checkInDate: _checkInDate!,
        checkOutDate: _checkOutDate!,
        adults: _adults,
        children: _children,
        rooms: _rooms,
        currency: _currency,
        maxResults: 50,
      );

      if (result.isSuccess) {
        _hotels = result.hotels ?? [];
        if (_hotels.isEmpty) {
          _setError('No hotels found in this area');
        }
      } else {
        _setError(result.errorMessage ?? 'Failed to search hotels');
      }
    } catch (e) {
      _setError('An error occurred while searching hotels');
      if (kDebugMode) {
        print('Hotel location search error: $e');
      }
    } finally {
      _setLoading(false);
    }
  }

  // Get hotel details
  Future<void> getHotelDetails(String hotelId) async {
    _setLoading(true);
    _setError(null);

    try {
      final result = await _amadeusService.getHotelDetails(hotelId);

      if (result.isSuccess) {
        _selectedHotelDetails = result.hotelDetails;
      } else {
        _setError(result.errorMessage ?? 'Failed to get hotel details');
      }
    } catch (e) {
      _setError('An error occurred while getting hotel details');
      if (kDebugMode) {
        print('Hotel details error: $e');
      }
    } finally {
      _setLoading(false);
    }
  }

  // Select hotel
  void selectHotel(HotelOffer hotel) {
    _selectedHotel = hotel;
    notifyListeners();
  }

  // Clear selected hotel
  void clearSelectedHotel() {
    _selectedHotel = null;
    _selectedHotelDetails = null;
    notifyListeners();
  }

  // Book hotel
  Future<bool> bookHotel({
    required String offerId,
    required List<Guest> guests,
    required PaymentDetails paymentDetails,
    required ContactInfo contactInfo,
  }) async {
    _setLoading(true);
    _setError(null);

    try {
      final result = await _amadeusService.bookHotel(
        offerId: offerId,
        guests: guests,
        paymentDetails: paymentDetails,
        contactInfo: contactInfo,
      );

      if (result.isSuccess && result.booking != null) {
        _bookings.add(result.booking!);
        notifyListeners();
        return true;
      } else {
        _setError(result.errorMessage ?? 'Failed to book hotel');
        return false;
      }
    } catch (e) {
      _setError('An error occurred while booking hotel');
      if (kDebugMode) {
        print('Hotel booking error: $e');
      }
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Clear all data
  void clearAll() {
    _hotels = [];
    _selectedHotel = null;
    _selectedHotelDetails = null;
    _errorMessage = null;
    notifyListeners();
  }

  // Reset filters
  void resetFilters() {
    _minPrice = 0;
    _maxPrice = 50000;
    _minRating = 0;
    _selectedAmenities = [];
    _sortBy = 'price';
    notifyListeners();
  }
}
