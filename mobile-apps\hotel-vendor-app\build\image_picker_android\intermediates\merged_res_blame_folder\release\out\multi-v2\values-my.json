{"logs": [{"outputFile": "io.flutter.plugins.imagepicker.image_picker_android-merged_res-27:/values-my/values-my.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\59cf662b2b8d845940ff382134f3256f\\transformed\\core-1.13.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,262,365,467,572,678,797", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "153,257,360,462,567,673,792,893"}}]}, {"outputFile": "io.flutter.plugins.imagepicker.image_picker_android-mergeReleaseResources-25:/values-my/values-my.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\59cf662b2b8d845940ff382134f3256f\\transformed\\core-1.13.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,262,365,467,572,678,797", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "153,257,360,462,567,673,792,893"}}]}]}