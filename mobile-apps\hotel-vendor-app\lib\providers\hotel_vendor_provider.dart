import 'package:flutter/foundation.dart';
import '../models/vendor_models.dart';
import '../services/hotel_registration_service.dart';

class HotelVendorProvider extends ChangeNotifier {
  final HotelRegistrationService _registrationService =
      HotelRegistrationService();

  // Current vendor
  HotelVendor? _currentVendor;
  HotelVendor? get currentVendor => _currentVendor;

  // Vendor hotels
  List<HotelRegistration> _vendorHotels = [];
  List<HotelRegistration> get vendorHotels => _vendorHotels;

  // Loading states
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  bool _isRegistering = false;
  bool get isRegistering => _isRegistering;

  // Error handling
  String? _errorMessage;
  String? get errorMessage => _errorMessage;

  // Analytics data
  Map<String, dynamic> _analytics = {};
  Map<String, dynamic> get analytics => _analytics;

  Map<String, dynamic> _earnings = {};
  Map<String, dynamic> get earnings => _earnings;

  /// Initialize vendor data
  Future<void> initializeVendor(String vendorId) async {
    _setLoading(true);
    try {
      _currentVendor = await _registrationService.getVendor(vendorId);
      if (_currentVendor != null) {
        await loadVendorHotels();
        await loadAnalytics();
        await loadEarnings();
      }
      _clearError();
    } catch (e) {
      _setError('Failed to initialize vendor data: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Load vendor hotels
  Future<void> loadVendorHotels() async {
    if (_currentVendor == null) return;

    try {
      _vendorHotels =
          await _registrationService.getVendorHotels(_currentVendor!.id);
      notifyListeners();
    } catch (e) {
      _setError('Failed to load hotels: $e');
    }
  }

  /// Register new vendor
  Future<bool> registerVendor(HotelVendor vendor) async {
    _setRegistering(true);
    try {
      final vendorId = await _registrationService.registerVendor(vendor);
      if (vendorId != null) {
        _currentVendor = vendor.copyWith(id: vendorId);
        _clearError();
        return true;
      }
      return false;
    } catch (e) {
      _setError('Failed to register vendor: $e');
      return false;
    } finally {
      _setRegistering(false);
    }
  }

  /// Register new hotel
  Future<bool> registerHotel(HotelRegistration hotel) async {
    _setLoading(true);
    try {
      final hotelId = await _registrationService.registerHotel(hotel);
      if (hotelId != null) {
        await loadVendorHotels(); // Refresh hotel list
        _clearError();
        return true;
      }
      return false;
    } catch (e) {
      _setError('Failed to register hotel: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Update hotel status
  Future<bool> updateHotelStatus(String hotelId, HotelStatus status) async {
    try {
      final success =
          await _registrationService.updateHotelStatus(hotelId, status);
      if (success) {
        await loadVendorHotels(); // Refresh hotel list
        _clearError();
      }
      return success;
    } catch (e) {
      _setError('Failed to update hotel status: $e');
      return false;
    }
  }

  /// Update vendor status
  Future<bool> updateVendorStatus(VendorStatus status) async {
    if (_currentVendor == null) return false;

    try {
      final success = await _registrationService.updateVendorStatus(
          _currentVendor!.id, status);
      if (success) {
        _currentVendor = HotelVendor(
          id: _currentVendor!.id,
          firstName: _currentVendor!.firstName,
          lastName: _currentVendor!.lastName,
          email: _currentVendor!.email,
          phone: _currentVendor!.phone,
          businessName: _currentVendor!.businessName,
          businessRegistrationNumber:
              _currentVendor!.businessRegistrationNumber,
          panNumber: _currentVendor!.panNumber,
          gstNumber: _currentVendor!.gstNumber,
          address: _currentVendor!.address,
          hotelIds: _currentVendor!.hotelIds,
          status: status,
          createdAt: _currentVendor!.createdAt,
          verifiedAt: _currentVendor!.verifiedAt,
          bankDetails: _currentVendor!.bankDetails,
        );
        notifyListeners();
        _clearError();
      }
      return success;
    } catch (e) {
      _setError('Failed to update vendor status: $e');
      return false;
    }
  }

  /// Update bank details
  Future<bool> updateBankDetails(BankDetails bankDetails) async {
    if (_currentVendor == null) return false;

    try {
      final success = await _registrationService.updateBankDetails(
          _currentVendor!.id, bankDetails);
      if (success) {
        _currentVendor = HotelVendor(
          id: _currentVendor!.id,
          firstName: _currentVendor!.firstName,
          lastName: _currentVendor!.lastName,
          email: _currentVendor!.email,
          phone: _currentVendor!.phone,
          businessName: _currentVendor!.businessName,
          businessRegistrationNumber:
              _currentVendor!.businessRegistrationNumber,
          panNumber: _currentVendor!.panNumber,
          gstNumber: _currentVendor!.gstNumber,
          address: _currentVendor!.address,
          hotelIds: _currentVendor!.hotelIds,
          status: _currentVendor!.status,
          createdAt: _currentVendor!.createdAt,
          verifiedAt: _currentVendor!.verifiedAt,
          bankDetails: bankDetails,
        );
        notifyListeners();
        _clearError();
      }
      return success;
    } catch (e) {
      _setError('Failed to update bank details: $e');
      return false;
    }
  }

  /// Load analytics data
  Future<void> loadAnalytics() async {
    if (_vendorHotels.isEmpty) return;

    try {
      // Load analytics for all hotels
      final allAnalytics = <String, dynamic>{
        'totalBookings': 0,
        'monthlyRevenue': 0.0,
        'averageOccupancy': 0.0,
        'averageRating': 0.0,
        'totalReviews': 0,
      };

      for (final hotel in _vendorHotels) {
        final hotelAnalytics =
            await _registrationService.getHotelAnalytics(hotel.id);
        allAnalytics['totalBookings'] += hotelAnalytics['totalBookings'] ?? 0;
        allAnalytics['monthlyRevenue'] +=
            hotelAnalytics['monthlyRevenue'] ?? 0.0;
        allAnalytics['totalReviews'] += hotelAnalytics['totalReviews'] ?? 0;
      }

      // Calculate averages
      if (_vendorHotels.isNotEmpty) {
        allAnalytics['averageOccupancy'] =
            allAnalytics['monthlyRevenue'] / _vendorHotels.length;
        allAnalytics['averageRating'] = allAnalytics['totalReviews'] > 0
            ? allAnalytics['averageRating'] / _vendorHotels.length
            : 0.0;
      }

      _analytics = allAnalytics;
      notifyListeners();
    } catch (e) {
      _setError('Failed to load analytics: $e');
    }
  }

  /// Load earnings data
  Future<void> loadEarnings() async {
    if (_currentVendor == null) return;

    try {
      _earnings =
          await _registrationService.getVendorEarnings(_currentVendor!.id);
      notifyListeners();
    } catch (e) {
      _setError('Failed to load earnings: $e');
    }
  }

  /// Get hotel by ID
  HotelRegistration? getHotelById(String hotelId) {
    try {
      return _vendorHotels.firstWhere((hotel) => hotel.id == hotelId);
    } catch (e) {
      return null;
    }
  }

  /// Get hotels by status
  List<HotelRegistration> getHotelsByStatus(HotelStatus status) {
    return _vendorHotels.where((hotel) => hotel.status == status).toList();
  }

  /// Get approved hotels count
  int get approvedHotelsCount => getHotelsByStatus(HotelStatus.approved).length;

  /// Get pending hotels count
  int get pendingHotelsCount => getHotelsByStatus(HotelStatus.pending).length;

  /// Get total revenue
  double get totalRevenue => _earnings['totalEarnings']?.toDouble() ?? 0.0;

  /// Get monthly revenue
  double get monthlyRevenue => _earnings['monthlyEarnings']?.toDouble() ?? 0.0;

  /// Get total bookings
  int get totalBookings => _analytics['totalBookings'] ?? 0;

  /// Get average occupancy
  double get averageOccupancy =>
      _analytics['averageOccupancy']?.toDouble() ?? 0.0;

  /// Refresh all data
  Future<void> refreshData() async {
    if (_currentVendor != null) {
      await initializeVendor(_currentVendor!.id);
    }
  }

  /// Clear all data (for logout)
  void clearData() {
    _currentVendor = null;
    _vendorHotels.clear();
    _analytics.clear();
    _earnings.clear();
    _clearError();
    notifyListeners();
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setRegistering(bool registering) {
    _isRegistering = registering;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}

// Extension to add copyWith method to HotelVendor
extension HotelVendorExtension on HotelVendor {
  HotelVendor copyWith({
    String? id,
    String? firstName,
    String? lastName,
    String? email,
    String? phone,
    String? businessName,
    String? businessRegistrationNumber,
    String? panNumber,
    String? gstNumber,
    VendorAddress? address,
    List<String>? hotelIds,
    VendorStatus? status,
    DateTime? createdAt,
    DateTime? verifiedAt,
    BankDetails? bankDetails,
  }) {
    return HotelVendor(
      id: id ?? this.id,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      businessName: businessName ?? this.businessName,
      businessRegistrationNumber:
          businessRegistrationNumber ?? this.businessRegistrationNumber,
      panNumber: panNumber ?? this.panNumber,
      gstNumber: gstNumber ?? this.gstNumber,
      address: address ?? this.address,
      hotelIds: hotelIds ?? this.hotelIds,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      verifiedAt: verifiedAt ?? this.verifiedAt,
      bankDetails: bankDetails ?? this.bankDetails,
    );
  }
}
