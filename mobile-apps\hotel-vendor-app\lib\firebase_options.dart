// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAD_X8xZZ0wW7k2jMwV_ytwo8ncd2l0u4I',
    appId: '1:408299360705:web:7ea9f01eec6c08d6dd5f40',
    messagingSenderId: '408299360705',
    projectId: 'linkinblink-f544a',
    authDomain: 'linkinblink-f544a.firebaseapp.com',
    storageBucket: 'linkinblink-f544a.firebasestorage.app',
    measurementId: 'G-BX9K1P9716',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBpMQS5vgW9f39NNmcN-XFHALaN1_ar5bg',
    appId: '1:408299360705:android:b719d1b14efe458bdd5f40',
    messagingSenderId: '408299360705',
    projectId: 'linkinblink-f544a',
    storageBucket: 'linkinblink-f544a.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCBSJWU1PbIpXVBh68J09NuyIfUZgsPg6Y',
    appId: '1:408299360705:ios:58baa67014078344dd5f40',
    messagingSenderId: '408299360705',
    projectId: 'linkinblink-f544a',
    storageBucket: 'linkinblink-f544a.firebasestorage.app',
    iosClientId: '408299360705-i9pgis55dm8143u3emnn7pkglt2t75u9.apps.googleusercontent.com',
    iosBundleId: 'com.example.customerApp',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyCBSJWU1PbIpXVBh68J09NuyIfUZgsPg6Y',
    appId: '1:408299360705:ios:58baa67014078344dd5f40',
    messagingSenderId: '408299360705',
    projectId: 'linkinblink-f544a',
    storageBucket: 'linkinblink-f544a.firebasestorage.app',
    iosClientId: '408299360705-i9pgis55dm8143u3emnn7pkglt2t75u9.apps.googleusercontent.com',
    iosBundleId: 'com.example.customerApp',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyAD_X8xZZ0wW7k2jMwV_ytwo8ncd2l0u4I',
    appId: '1:408299360705:web:b62736b3ba5ad4b2dd5f40',
    messagingSenderId: '408299360705',
    projectId: 'linkinblink-f544a',
    authDomain: 'linkinblink-f544a.firebaseapp.com',
    storageBucket: 'linkinblink-f544a.firebasestorage.app',
    measurementId: 'G-XXG5Z7X2TS',
  );
}
