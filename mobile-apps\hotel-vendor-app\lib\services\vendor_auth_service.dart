import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/vendor_models.dart';
import 'hotel_registration_service.dart';

class VendorAuthService {
  static final VendorAuthService _instance = VendorAuthService._internal();
  factory VendorAuthService() => _instance;
  VendorAuthService._internal();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final HotelRegistrationService _registrationService =
      HotelRegistrationService();

  // Current user stream
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Current user
  User? get currentUser => _auth.currentUser;

  // Current vendor
  HotelVendor? _currentVendor;
  HotelVendor? get currentVendor => _currentVendor;

  /// Sign in with email and password
  Future<UserCredential?> signInWithEmailAndPassword(
      String email, String password) async {
    try {
      final credential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        await _loadCurrentVendor();
      }

      return credential;
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  /// Sign up with email and password
  Future<UserCredential?> signUpWithEmailAndPassword(
      String email, String password) async {
    try {
      final credential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Send email verification
      if (credential.user != null && !credential.user!.emailVerified) {
        await credential.user!.sendEmailVerification();
      }

      return credential;
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      _currentVendor = null;
      await _auth.signOut();
    } catch (e) {
      throw Exception('Failed to sign out: $e');
    }
  }

  /// Send password reset email
  Future<void> sendPasswordResetEmail(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  /// Send email verification
  Future<void> sendEmailVerification() async {
    try {
      final user = _auth.currentUser;
      if (user != null && !user.emailVerified) {
        await user.sendEmailVerification();
      }
    } catch (e) {
      throw Exception('Failed to send email verification: $e');
    }
  }

  /// Reload user to check email verification status
  Future<void> reloadUser() async {
    try {
      await _auth.currentUser?.reload();
    } catch (e) {
      throw Exception('Failed to reload user: $e');
    }
  }

  /// Check if email is verified
  bool get isEmailVerified => _auth.currentUser?.emailVerified ?? false;

  /// Create vendor profile after signup
  Future<bool> createVendorProfile(HotelVendor vendor) async {
    try {
      final user = _auth.currentUser;
      if (user == null) throw Exception('No authenticated user');

      // Create vendor document with user ID
      await _firestore
          .collection(HotelRegistrationService.vendorsCollection)
          .doc(user.uid)
          .set(vendor.toFirestore());

      _currentVendor = HotelVendor(
        id: user.uid,
        firstName: vendor.firstName,
        lastName: vendor.lastName,
        email: vendor.email,
        phone: vendor.phone,
        businessName: vendor.businessName,
        businessRegistrationNumber: vendor.businessRegistrationNumber,
        panNumber: vendor.panNumber,
        gstNumber: vendor.gstNumber,
        address: vendor.address,
        hotelIds: vendor.hotelIds,
        status: vendor.status,
        createdAt: vendor.createdAt,
        verifiedAt: vendor.verifiedAt,
        bankDetails: vendor.bankDetails,
      );
      return true;
    } catch (e) {
      throw Exception('Failed to create vendor profile: $e');
    }
  }

  /// Load current vendor data
  Future<void> _loadCurrentVendor() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      _currentVendor = await _registrationService.getVendor(user.uid);
    } catch (e) {
      print('Error loading current vendor: $e');
    }
  }

  /// Update vendor profile
  Future<bool> updateVendorProfile(Map<String, dynamic> updates) async {
    try {
      final user = _auth.currentUser;
      if (user == null) throw Exception('No authenticated user');

      await _firestore
          .collection(HotelRegistrationService.vendorsCollection)
          .doc(user.uid)
          .update(updates);

      // Reload vendor data
      await _loadCurrentVendor();
      return true;
    } catch (e) {
      throw Exception('Failed to update vendor profile: $e');
    }
  }

  /// Delete vendor account
  Future<bool> deleteAccount() async {
    try {
      final user = _auth.currentUser;
      if (user == null) throw Exception('No authenticated user');

      // Delete vendor document
      await _firestore
          .collection(HotelRegistrationService.vendorsCollection)
          .doc(user.uid)
          .delete();

      // Delete user account
      await user.delete();

      _currentVendor = null;
      return true;
    } catch (e) {
      throw Exception('Failed to delete account: $e');
    }
  }

  /// Update email
  Future<void> updateEmail(String newEmail) async {
    try {
      final user = _auth.currentUser;
      if (user == null) throw Exception('No authenticated user');

      await user.updateEmail(newEmail);

      // Update vendor document
      await updateVendorProfile({'email': newEmail});

      // Send verification to new email
      await sendEmailVerification();
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  /// Update password
  Future<void> updatePassword(String newPassword) async {
    try {
      final user = _auth.currentUser;
      if (user == null) throw Exception('No authenticated user');

      await user.updatePassword(newPassword);
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  /// Reauthenticate user (required for sensitive operations)
  Future<void> reauthenticate(String password) async {
    try {
      final user = _auth.currentUser;
      if (user == null || user.email == null) {
        throw Exception('No authenticated user');
      }

      final credential = EmailAuthProvider.credential(
        email: user.email!,
        password: password,
      );

      await user.reauthenticateWithCredential(credential);
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  /// Check if vendor profile exists
  Future<bool> hasVendorProfile() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      final doc = await _firestore
          .collection(HotelRegistrationService.vendorsCollection)
          .doc(user.uid)
          .get();

      return doc.exists;
    } catch (e) {
      return false;
    }
  }

  /// Get vendor status
  VendorStatus? get vendorStatus => _currentVendor?.status;

  /// Check if vendor is verified
  bool get isVendorVerified =>
      vendorStatus == VendorStatus.verified ||
      vendorStatus == VendorStatus.active;

  /// Check if vendor can register hotels
  bool get canRegisterHotels => isVendorVerified;

  /// Initialize auth service (call on app start)
  Future<void> initialize() async {
    try {
      // Listen to auth state changes
      _auth.authStateChanges().listen((user) async {
        if (user != null) {
          await _loadCurrentVendor();
        } else {
          _currentVendor = null;
        }
      });

      // Load current vendor if user is already signed in
      if (_auth.currentUser != null) {
        await _loadCurrentVendor();
      }
    } catch (e) {
      print('Error initializing auth service: $e');
    }
  }

  /// Handle Firebase Auth exceptions
  String _handleAuthException(FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return 'No user found with this email address.';
      case 'wrong-password':
        return 'Incorrect password.';
      case 'email-already-in-use':
        return 'An account already exists with this email address.';
      case 'weak-password':
        return 'Password is too weak. Please choose a stronger password.';
      case 'invalid-email':
        return 'Invalid email address format.';
      case 'user-disabled':
        return 'This account has been disabled.';
      case 'too-many-requests':
        return 'Too many failed attempts. Please try again later.';
      case 'operation-not-allowed':
        return 'This operation is not allowed.';
      case 'requires-recent-login':
        return 'Please sign in again to perform this action.';
      case 'email-not-verified':
        return 'Please verify your email address before continuing.';
      default:
        return 'Authentication error: ${e.message}';
    }
  }

  /// Validate vendor registration data
  static Map<String, String?> validateVendorData({
    required String firstName,
    required String lastName,
    required String email,
    required String phone,
    required String businessName,
    required String businessRegistrationNumber,
    required String panNumber,
    required String gstNumber,
    required String street,
    required String city,
    required String state,
    required String pincode,
  }) {
    final errors = <String, String?>{};

    // Name validation
    if (firstName.trim().isEmpty) {
      errors['firstName'] = 'First name is required';
    }
    if (lastName.trim().isEmpty) {
      errors['lastName'] = 'Last name is required';
    }

    // Email validation
    if (email.trim().isEmpty) {
      errors['email'] = 'Email is required';
    } else if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email)) {
      errors['email'] = 'Invalid email format';
    }

    // Phone validation
    if (phone.trim().isEmpty) {
      errors['phone'] = 'Phone number is required';
    } else if (!RegExp(r'^\+?[1-9]\d{1,14}$')
        .hasMatch(phone.replaceAll(RegExp(r'[\s-()]'), ''))) {
      errors['phone'] = 'Invalid phone number format';
    }

    // Business validation
    if (businessName.trim().isEmpty) {
      errors['businessName'] = 'Business name is required';
    }
    if (businessRegistrationNumber.trim().isEmpty) {
      errors['businessRegistrationNumber'] =
          'Business registration number is required';
    }

    // PAN validation
    if (panNumber.trim().isEmpty) {
      errors['panNumber'] = 'PAN number is required';
    } else if (!RegExp(r'^[A-Z]{5}[0-9]{4}[A-Z]{1}$')
        .hasMatch(panNumber.toUpperCase())) {
      errors['panNumber'] = 'Invalid PAN format (e.g., **********)';
    }

    // GST validation
    if (gstNumber.trim().isEmpty) {
      errors['gstNumber'] = 'GST number is required';
    } else if (!RegExp(
            r'^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$')
        .hasMatch(gstNumber.toUpperCase())) {
      errors['gstNumber'] = 'Invalid GST format';
    }

    // Address validation
    if (street.trim().isEmpty) {
      errors['street'] = 'Street address is required';
    }
    if (city.trim().isEmpty) {
      errors['city'] = 'City is required';
    }
    if (state.trim().isEmpty) {
      errors['state'] = 'State is required';
    }
    if (pincode.trim().isEmpty) {
      errors['pincode'] = 'Pincode is required';
    } else if (!RegExp(r'^[1-9][0-9]{5}$').hasMatch(pincode)) {
      errors['pincode'] = 'Invalid pincode format';
    }

    return errors;
  }
}
