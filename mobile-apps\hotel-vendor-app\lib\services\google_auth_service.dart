import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'hybrid_auth_service.dart';
import 'firebase_rooms_service.dart';
import 'firebase_bookings_service.dart';
import 'firebase_guests_service.dart';

/// Google Sign-In service for hotel vendor app
class GoogleAuthService {
  static final GoogleAuthService _instance = GoogleAuthService._internal();
  factory GoogleAuthService() => _instance;
  GoogleAuthService._internal();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  GoogleSignIn? _googleSignIn;
  final HybridAuthService _hybridAuth = HybridAuthService();

  // Initialize Google Sign-In
  void _initializeGoogleSignIn() {
    if (_googleSignIn == null) {
      try {
        if (kIsWeb) {
          // For web, we'll use Firebase Auth directly
          _googleSignIn = null;
        } else {
          // For mobile platforms
          _googleSignIn = GoogleSignIn(
            scopes: ['email', 'profile'],
          );
        }
      } catch (e) {
        if (kDebugMode) {
          print('Google Sign-In initialization failed: $e');
        }
        _googleSignIn = null;
      }
    }
  }

  /// Sign in with Google
  Future<Map<String, dynamic>?> signInWithGoogle() async {
    try {
      if (kIsWeb) {
        // For web, use Firebase Auth's built-in Google provider
        return await _signInWithGoogleWeb();
      } else {
        // For mobile, use Google Sign-In package
        return await _signInWithGoogleMobile();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Google sign in error: $e');
      }
      return null;
    }
  }

  /// Web Google Sign-In implementation
  Future<Map<String, dynamic>?> _signInWithGoogleWeb() async {
    try {
      final GoogleAuthProvider googleProvider = GoogleAuthProvider();
      googleProvider.addScope('email');
      googleProvider.addScope('profile');

      final userCredential = await _auth.signInWithPopup(googleProvider);

      if (userCredential.user != null) {
        return await _processGoogleUser(userCredential.user!);
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Google Sign-In web error: $e');
      }
      return null;
    }
  }

  /// Mobile Google Sign-In implementation
  Future<Map<String, dynamic>?> _signInWithGoogleMobile() async {
    try {
      _initializeGoogleSignIn();

      if (_googleSignIn == null) {
        throw Exception('Google Sign-In not available');
      }

      final GoogleSignInAccount? googleUser = await _googleSignIn!.signIn();
      if (googleUser == null) {
        throw Exception('Google sign in was cancelled');
      }

      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      final userCredential = await _auth.signInWithCredential(credential);

      if (userCredential.user != null) {
        return await _processGoogleUser(userCredential.user!);
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Google Sign-In mobile error: $e');
      }
      return null;
    }
  }

  /// Process Google user and create/update vendor profile
  Future<Map<String, dynamic>?> _processGoogleUser(User firebaseUser) async {
    try {
      // Check if user already exists in our hybrid auth system
      final existingUser = await _checkExistingUser(firebaseUser.email!);
      if (existingUser != null) {
        // User exists, log them in using the special Google auth password
        final loginResult =
            await _hybridAuth.login(existingUser['email'], 'google_auth');
        return loginResult;
      }

      // Create new vendor profile from Google account
      final newVendor = await _createVendorFromGoogleUser(firebaseUser);
      if (newVendor != null) {
        // Log in the newly created user
        final loginResult =
            await _hybridAuth.login(newVendor['email'], 'google_auth');
        return loginResult;
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error processing Google user: $e');
      }
      return null;
    }
  }

  /// Check if user exists in hybrid auth system
  Future<Map<String, dynamic>?> _checkExistingUser(String email) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final usersJson = prefs.getString('registered_users');
      if (usersJson != null) {
        final List<dynamic> users = json.decode(usersJson);
        for (final user in users) {
          if (user['email'] == email) {
            return Map<String, dynamic>.from(user);
          }
        }
      }
      return null;
    } catch (e) {
      if (kDebugMode) print('Error checking existing user: $e');
      return null;
    }
  }

  /// Create new vendor from Google user
  Future<Map<String, dynamic>?> _createVendorFromGoogleUser(
      User firebaseUser) async {
    try {
      final userId = 'google_${DateTime.now().millisecondsSinceEpoch}';
      final displayName = firebaseUser.displayName ?? '';
      final nameParts = displayName.split(' ');
      final firstName = nameParts.isNotEmpty ? nameParts.first : 'Google';
      final lastName =
          nameParts.length > 1 ? nameParts.skip(1).join(' ') : 'User';

      final newVendor = {
        'id': userId,
        'firebaseUid': firebaseUser.uid,
        'firstName': firstName,
        'lastName': lastName,
        'email': firebaseUser.email!,
        'password': 'google_auth', // Special marker for Google auth
        'phone': firebaseUser.phoneNumber ?? '',
        'businessName': '$firstName $lastName Hotels',
        'status': 'verified',
        'authProvider': 'google',
        'profileImage': firebaseUser.photoURL,
        'createdAt': DateTime.now().toIso8601String(),
        'verifiedAt': DateTime.now().toIso8601String(),
        'hotelIds': <String>[],
        'bankDetails': null,
      };

      // Save to hybrid auth system
      final prefs = await SharedPreferences.getInstance();
      final usersJson = prefs.getString('registered_users');
      final List<dynamic> users =
          usersJson != null ? json.decode(usersJson) : [];
      users.add(newVendor);
      await prefs.setString('registered_users', json.encode(users));

      // Auto-create a basic hotel for the vendor
      await _createBasicHotelForGoogleUser(
          userId, newVendor['businessName'] as String);

      // Initialize Firebase data for the new vendor
      try {
        // Initialize rooms, bookings, and guests in Firebase
        final roomsService = FirebaseRoomsService();
        final bookingsService = FirebaseBookingsService();
        final guestsService = FirebaseGuestsService();

        await roomsService.initializeNewUserRooms();
        await bookingsService.initializeNewUserBookings();
        await guestsService.initializeNewUserGuests();

        if (kDebugMode) print('Firebase data initialized for new Google user');
      } catch (e) {
        if (kDebugMode) print('Error initializing Firebase data: $e');
      }

      if (kDebugMode) {
        print('Google user registered successfully: ${newVendor['email']}');
      }

      return newVendor;
    } catch (e) {
      if (kDebugMode) {
        print('Error creating vendor from Google user: $e');
      }
      return null;
    }
  }

  /// Create basic hotel for Google user
  Future<void> _createBasicHotelForGoogleUser(
      String userId, String businessName) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final hotelId = 'hotel_${DateTime.now().millisecondsSinceEpoch}';

      final hotel = {
        'id': hotelId,
        'vendorId': userId,
        'name': businessName,
        'description':
            'Welcome to $businessName! We provide excellent hospitality services.',
        'address': 'Please update your hotel address',
        'city': 'Your City',
        'state': 'Your State',
        'country': 'India',
        'pincode': '000000',
        'phone': 'Please update phone number',
        'email': 'Please update email',
        'website': '',
        'rating': 4.0,
        'totalRooms': 4,
        'amenities': ['WiFi', 'AC', 'TV', 'Room Service'],
        'images': [
          'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800&h=600&fit=crop',
        ],
        'roomTypes': [
          {
            'type': 'Standard Room',
            'totalRooms': 2,
            'basePrice': 3500.0,
            'amenities': ['WiFi', 'AC', 'TV'],
          },
          {
            'type': 'Deluxe Room',
            'totalRooms': 2,
            'basePrice': 5000.0,
            'amenities': ['WiFi', 'AC', 'TV', 'Mini Bar'],
          },
        ],
        'policies': {
          'checkIn': '14:00',
          'checkOut': '11:00',
          'cancellation': '24 hours before check-in',
        },
        'status': 'active',
        'createdAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
      };

      // Save hotel data
      final hotelsJson = prefs.getString('user_hotels_$userId');
      final List<dynamic> hotels =
          hotelsJson != null ? json.decode(hotelsJson) : [];
      hotels.add(hotel);
      await prefs.setString('user_hotels_$userId', json.encode(hotels));

      if (kDebugMode) {
        print('Basic hotel created for Google user: $hotelId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error creating basic hotel for Google user: $e');
      }
    }
  }

  /// Sign out from Google
  Future<void> signOut() async {
    try {
      await _auth.signOut();
      if (_googleSignIn != null) {
        await _googleSignIn!.signOut();
      }
      await _hybridAuth.logout();
    } catch (e) {
      if (kDebugMode) {
        print('Error signing out from Google: $e');
      }
    }
  }

  /// Check if user is signed in with Google
  bool isSignedInWithGoogle() {
    final user = _auth.currentUser;
    return user != null &&
        user.providerData.any((info) => info.providerId == 'google.com');
  }

  /// Get current Firebase user
  User? get currentFirebaseUser => _auth.currentUser;
}
