<svg width="375" height="812" viewBox="0 0 375 812" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Ocean wave gradient -->
  <defs>
    <linearGradient id="bg2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="wave1" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.3" />
      <stop offset="50%" style="stop-color:#ffffff;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.3" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="375" height="812" fill="url(#bg2)"/>
  
  <!-- Wave patterns -->
  <path d="M0,200 Q93.75,150 187.5,200 T375,200 L375,250 Q281.25,200 187.5,250 T0,250 Z" fill="url(#wave1)"/>
  <path d="M0,400 Q93.75,350 187.5,400 T375,400 L375,450 Q281.25,400 187.5,450 T0,450 Z" fill="white" opacity="0.1"/>
  <path d="M0,600 Q93.75,550 187.5,600 T375,600 L375,650 Q281.25,600 187.5,650 T0,650 Z" fill="url(#wave1)"/>
  
  <!-- Beach elements -->
  <g transform="translate(100, 100)">
    <!-- Palm tree -->
    <rect x="15" y="30" width="4" height="40" fill="#8B4513" opacity="0.3"/>
    <ellipse cx="17" cy="25" rx="12" ry="8" fill="#228B22" opacity="0.3"/>
    <ellipse cx="17" cy="20" rx="10" ry="6" fill="#32CD32" opacity="0.3"/>
  </g>
  
  <!-- Sun -->
  <circle cx="300" cy="120" r="25" fill="#FFD700" opacity="0.4"/>
  <g transform="translate(300, 120)">
    <line x1="-35" y1="0" x2="-30" y2="0" stroke="#FFD700" stroke-width="2" opacity="0.4"/>
    <line x1="30" y1="0" x2="35" y2="0" stroke="#FFD700" stroke-width="2" opacity="0.4"/>
    <line x1="0" y1="-35" x2="0" y2="-30" stroke="#FFD700" stroke-width="2" opacity="0.4"/>
    <line x1="0" y1="30" x2="0" y2="35" stroke="#FFD700" stroke-width="2" opacity="0.4"/>
  </g>
  
  <!-- Floating bubbles -->
  <circle cx="80" cy="300" r="8" fill="white" opacity="0.2"/>
  <circle cx="280" cy="450" r="12" fill="white" opacity="0.15"/>
  <circle cx="150" cy="550" r="6" fill="white" opacity="0.25"/>
</svg>
