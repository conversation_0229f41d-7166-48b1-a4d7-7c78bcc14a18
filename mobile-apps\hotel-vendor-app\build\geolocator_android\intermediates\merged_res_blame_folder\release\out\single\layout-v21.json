[{"merged": "com.baseflow.geolocator.geolocator_android-merged_res-27:/layout-v21/notification_template_custom_big.xml", "source": "com.baseflow.geolocator.geolocator_android-core-1.13.1-11:/layout-v21/notification_template_custom_big.xml"}, {"merged": "com.baseflow.geolocator.geolocator_android-merged_res-27:/layout-v21/notification_template_icon_group.xml", "source": "com.baseflow.geolocator.geolocator_android-core-1.13.1-11:/layout-v21/notification_template_icon_group.xml"}, {"merged": "com.baseflow.geolocator.geolocator_android-merged_res-27:/layout-v21/notification_action_tombstone.xml", "source": "com.baseflow.geolocator.geolocator_android-core-1.13.1-11:/layout-v21/notification_action_tombstone.xml"}, {"merged": "com.baseflow.geolocator.geolocator_android-merged_res-27:/layout-v21/notification_action.xml", "source": "com.baseflow.geolocator.geolocator_android-core-1.13.1-11:/layout-v21/notification_action.xml"}]