import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'hybrid_auth_service.dart';

/// Dynamic data service that manages all user-specific data
class DynamicDataService {
  static final DynamicDataService _instance = DynamicDataService._internal();
  factory DynamicDataService() => _instance;
  DynamicDataService._internal();

  final _authService = HybridAuthService();

  // Storage keys
  static const String _hotelsPrefix = 'user_hotels_';
  static const String _bookingsPrefix = 'user_bookings_';
  static const String _guestsPrefix = 'user_guests_';
  static const String _analyticsPrefix = 'user_analytics_';

  /// Get current user ID
  String? get _currentUserId => _authService.currentUser?['id'];

  /// Get user's hotels
  Future<List<Map<String, dynamic>>> getUserHotels() async {
    if (_currentUserId == null) return [];

    try {
      final prefs = await SharedPreferences.getInstance();
      final hotelsJson = prefs.getString('$_hotelsPrefix$_currentUserId');
      if (hotelsJson != null) {
        final List<dynamic> hotelsList = json.decode(hotelsJson);
        return hotelsList.cast<Map<String, dynamic>>();
      }
      return [];
    } catch (e) {
      if (kDebugMode) print('Error getting user hotels: $e');
      return [];
    }
  }

  /// Add hotel for current user
  Future<bool> addHotel(Map<String, dynamic> hotelData) async {
    if (_currentUserId == null) return false;

    try {
      final hotels = await getUserHotels();
      final hotelId = 'hotel_${DateTime.now().millisecondsSinceEpoch}';

      final newHotel = {
        ...hotelData,
        'id': hotelId,
        'vendorId': _currentUserId,
        'status': 'approved',
        'createdAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
      };

      hotels.add(newHotel);

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
          '$_hotelsPrefix$_currentUserId', json.encode(hotels));

      return true;
    } catch (e) {
      if (kDebugMode) print('Error adding hotel: $e');
      return false;
    }
  }

  /// Get user's bookings
  Future<List<Map<String, dynamic>>> getUserBookings() async {
    if (_currentUserId == null) return [];

    try {
      final prefs = await SharedPreferences.getInstance();
      final bookingsJson = prefs.getString('$_bookingsPrefix$_currentUserId');
      if (bookingsJson != null) {
        final List<dynamic> bookingsList = json.decode(bookingsJson);
        return bookingsList.cast<Map<String, dynamic>>();
      }
      return [];
    } catch (e) {
      if (kDebugMode) print('Error getting user bookings: $e');
      return [];
    }
  }

  /// Add booking for current user
  Future<bool> addBooking(Map<String, dynamic> bookingData) async {
    if (_currentUserId == null) return false;

    try {
      final bookings = await getUserBookings();
      final bookingId = 'booking_${DateTime.now().millisecondsSinceEpoch}';

      final newBooking = {
        ...bookingData,
        'id': bookingId,
        'vendorId': _currentUserId,
        'bookingDate': DateTime.now().toIso8601String(),
        'createdAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
      };

      bookings.add(newBooking);

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
          '$_bookingsPrefix$_currentUserId', json.encode(bookings));

      return true;
    } catch (e) {
      if (kDebugMode) print('Error adding booking: $e');
      return false;
    }
  }

  /// Update booking status
  Future<bool> updateBookingStatus(String bookingId, String status) async {
    if (_currentUserId == null) return false;

    try {
      final bookings = await getUserBookings();
      final bookingIndex = bookings.indexWhere((b) => b['id'] == bookingId);

      if (bookingIndex == -1) return false;

      bookings[bookingIndex]['status'] = status;
      bookings[bookingIndex]['updatedAt'] = DateTime.now().toIso8601String();

      if (status == 'checked_in') {
        bookings[bookingIndex]['actualCheckInTime'] =
            DateTime.now().toIso8601String();
      } else if (status == 'checked_out') {
        bookings[bookingIndex]['actualCheckOutTime'] =
            DateTime.now().toIso8601String();
      }

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
          '$_bookingsPrefix$_currentUserId', json.encode(bookings));

      return true;
    } catch (e) {
      if (kDebugMode) print('Error updating booking status: $e');
      return false;
    }
  }

  /// Check in guest
  Future<bool> checkInGuest(String bookingId, {String? aadharNumber}) async {
    if (_currentUserId == null) return false;

    try {
      final bookings = await getUserBookings();
      final bookingIndex = bookings.indexWhere((b) => b['id'] == bookingId);

      if (bookingIndex == -1) return false;

      bookings[bookingIndex]['status'] = 'checked_in';
      bookings[bookingIndex]['actualCheckInTime'] =
          DateTime.now().toIso8601String();
      bookings[bookingIndex]['updatedAt'] = DateTime.now().toIso8601String();

      if (aadharNumber != null) {
        bookings[bookingIndex]['aadharNumber'] = aadharNumber;
        bookings[bookingIndex]['aadharVerified'] = true;
        bookings[bookingIndex]['aadharVerificationTime'] =
            DateTime.now().toIso8601String();
      }

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
          '$_bookingsPrefix$_currentUserId', json.encode(bookings));

      return true;
    } catch (e) {
      if (kDebugMode) print('Error checking in guest: $e');
      return false;
    }
  }

  /// Check out guest
  Future<bool> checkOutGuest(String bookingId) async {
    if (_currentUserId == null) return false;

    try {
      final bookings = await getUserBookings();
      final bookingIndex = bookings.indexWhere((b) => b['id'] == bookingId);

      if (bookingIndex == -1) return false;

      bookings[bookingIndex]['status'] = 'checked_out';
      bookings[bookingIndex]['actualCheckOutTime'] =
          DateTime.now().toIso8601String();
      bookings[bookingIndex]['updatedAt'] = DateTime.now().toIso8601String();

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
          '$_bookingsPrefix$_currentUserId', json.encode(bookings));

      return true;
    } catch (e) {
      if (kDebugMode) print('Error checking out guest: $e');
      return false;
    }
  }

  /// Verify Aadhar for guest
  Future<bool> verifyAadhar(String bookingId, String aadharNumber) async {
    if (_currentUserId == null) return false;

    try {
      final bookings = await getUserBookings();
      final bookingIndex = bookings.indexWhere((b) => b['id'] == bookingId);

      if (bookingIndex == -1) return false;

      bookings[bookingIndex]['aadharNumber'] = aadharNumber;
      bookings[bookingIndex]['aadharVerified'] = true;
      bookings[bookingIndex]['aadharVerificationTime'] =
          DateTime.now().toIso8601String();
      bookings[bookingIndex]['updatedAt'] = DateTime.now().toIso8601String();

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
          '$_bookingsPrefix$_currentUserId', json.encode(bookings));

      return true;
    } catch (e) {
      if (kDebugMode) print('Error verifying Aadhar: $e');
      return false;
    }
  }

  /// Get bookings by status
  Future<List<Map<String, dynamic>>> getBookingsByStatus(String status) async {
    final allBookings = await getUserBookings();
    if (status == 'all') return allBookings;
    return allBookings.where((booking) => booking['status'] == status).toList();
  }

  /// Get today's check-ins
  Future<List<Map<String, dynamic>>> getTodayCheckIns() async {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = DateTime(today.year, today.month, today.day, 23, 59, 59);

    final allBookings = await getUserBookings();
    return allBookings.where((booking) {
      try {
        final checkInDate = DateTime.parse(booking['checkInDate']);
        return checkInDate.isAfter(startOfDay) &&
            checkInDate.isBefore(endOfDay) &&
            booking['status'] == 'confirmed';
      } catch (e) {
        return false;
      }
    }).toList();
  }

  /// Get today's check-outs
  Future<List<Map<String, dynamic>>> getTodayCheckOuts() async {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = DateTime(today.year, today.month, today.day, 23, 59, 59);

    final allBookings = await getUserBookings();
    return allBookings.where((booking) {
      try {
        final checkOutDate = DateTime.parse(booking['checkOutDate']);
        return checkOutDate.isAfter(startOfDay) &&
            checkOutDate.isBefore(endOfDay) &&
            booking['status'] == 'checked_in';
      } catch (e) {
        return false;
      }
    }).toList();
  }

  /// Get current guests (checked in)
  Future<List<Map<String, dynamic>>> getCurrentGuests() async {
    final allBookings = await getUserBookings();
    return allBookings
        .where((booking) => booking['status'] == 'checked_in')
        .toList();
  }

  /// Search bookings
  Future<List<Map<String, dynamic>>> searchBookings(String searchTerm) async {
    final allBookings = await getUserBookings();
    final searchLower = searchTerm.toLowerCase();

    return allBookings.where((booking) {
      final customerName =
          (booking['customerName'] ?? '').toString().toLowerCase();
      final customerPhone =
          (booking['customerPhone'] ?? '').toString().toLowerCase();
      final roomNumber = (booking['roomNumber'] ?? '').toString().toLowerCase();

      return customerName.contains(searchLower) ||
          customerPhone.contains(searchLower) ||
          roomNumber.contains(searchLower);
    }).toList();
  }

  /// Get analytics for current user
  Future<Map<String, dynamic>> getAnalytics() async {
    if (_currentUserId == null) return {};

    try {
      final bookings = await getUserBookings();
      final hotels = await getUserHotels();

      final now = DateTime.now();
      final startOfMonth = DateTime(now.year, now.month, 1);

      // Calculate monthly bookings
      final monthlyBookings = bookings.where((booking) {
        try {
          final bookingDate = DateTime.parse(booking['bookingDate']);
          return bookingDate.isAfter(startOfMonth);
        } catch (e) {
          return false;
        }
      }).toList();

      // Calculate revenue
      final totalRevenue = bookings.fold<double>(0.0, (sum, booking) {
        return sum + (booking['totalAmount']?.toDouble() ?? 0.0);
      });

      final monthlyRevenue = monthlyBookings.fold<double>(0.0, (sum, booking) {
        return sum + (booking['totalAmount']?.toDouble() ?? 0.0);
      });

      // Calculate occupancy
      final currentGuests = await getCurrentGuests();
      final totalRooms = hotels.fold<int>(0, (sum, hotel) {
        final roomTypes = hotel['roomTypes'] as List<dynamic>? ?? [];
        return sum +
            roomTypes.fold<int>(0, (roomSum, roomType) {
              final totalRoomsValue = roomType['totalRooms'];
              final roomCount = totalRoomsValue is int
                  ? totalRoomsValue
                  : (totalRoomsValue is double ? totalRoomsValue.toInt() : 0);
              return roomSum + roomCount;
            });
      });

      final occupancyRate =
          totalRooms > 0 ? (currentGuests.length / totalRooms) * 100 : 0.0;

      return {
        'totalHotels': hotels.length,
        'approvedHotels': hotels.where((h) => h['status'] == 'approved').length,
        'totalBookings': bookings.length,
        'monthlyBookings': monthlyBookings.length,
        'totalRevenue': totalRevenue,
        'monthlyRevenue': monthlyRevenue,
        'averageBookingValue':
            bookings.isNotEmpty ? totalRevenue / bookings.length : 0.0,
        'occupancyRate': occupancyRate,
        'totalRooms': totalRooms,
        'occupiedRooms': currentGuests.length,
        'availableRooms': totalRooms - currentGuests.length,
        'todayCheckIns': (await getTodayCheckIns()).length,
        'todayCheckOuts': (await getTodayCheckOuts()).length,
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      if (kDebugMode) print('Error getting analytics: $e');
      return {};
    }
  }

  /// Clear all user data (for logout)
  Future<void> clearUserData() async {
    if (_currentUserId == null) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('$_hotelsPrefix$_currentUserId');
      await prefs.remove('$_bookingsPrefix$_currentUserId');
      await prefs.remove('$_guestsPrefix$_currentUserId');
      await prefs.remove('$_analyticsPrefix$_currentUserId');
    } catch (e) {
      if (kDebugMode) print('Error clearing user data: $e');
    }
  }

  /// Initialize user with sample data (only for new users)
  Future<void> initializeNewUser() async {
    if (_currentUserId == null) return;

    final existingBookings = await getUserBookings();
    if (existingBookings.isNotEmpty) return; // User already has data

    // Add some initial bookings for demonstration
    final sampleBookings = [
      {
        'customerName': 'John Smith',
        'customerEmail': '<EMAIL>',
        'customerPhone': '+91 9876543210',
        'roomNumber': '101',
        'roomType': 'Deluxe Room',
        'checkInDate':
            DateTime.now().subtract(const Duration(days: 2)).toIso8601String(),
        'checkOutDate':
            DateTime.now().add(const Duration(days: 1)).toIso8601String(),
        'numberOfGuests': 2,
        'status': 'checked_in',
        'aadharVerified': true,
        'aadharNumber': '1234-5678-9012',
        'totalAmount': 4500.0,
        'specialRequests': 'Late checkout requested',
      },
      {
        'customerName': 'Sarah Johnson',
        'customerEmail': '<EMAIL>',
        'customerPhone': '+91 8765432109',
        'roomNumber': '205',
        'roomType': 'Suite',
        'checkInDate':
            DateTime.now().add(const Duration(days: 1)).toIso8601String(),
        'checkOutDate':
            DateTime.now().add(const Duration(days: 3)).toIso8601String(),
        'numberOfGuests': 1,
        'status': 'confirmed',
        'aadharVerified': false,
        'totalAmount': 7500.0,
        'specialRequests': 'Extra pillows',
      },
    ];

    for (final booking in sampleBookings) {
      await addBooking(booking);
    }
  }
}
