{"logs": [{"outputFile": "com.example.hotel_vendor_app-mergeDebugResources-51:/values-ta/values-ta.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\772e2867940e4356072472f7dc271176\\transformed\\appcompat-1.1.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,320,435,524,635,756,835,912,1010,1109,1204,1298,1406,1506,1608,1702,1800,1898,1978,2086,2189,2288,2404,2507,2612,2769,2871", "endColumns": "112,101,114,88,110,120,78,76,97,98,94,93,107,99,101,93,97,97,79,107,102,98,115,102,104,156,101,80", "endOffsets": "213,315,430,519,630,751,830,907,1005,1104,1199,1293,1401,1501,1603,1697,1795,1893,1973,2081,2184,2283,2399,2502,2607,2764,2866,2947"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,320,435,524,635,756,835,912,1010,1109,1204,1298,1406,1506,1608,1702,1800,1898,1978,2086,2189,2288,2404,2507,2612,2769,6905", "endColumns": "112,101,114,88,110,120,78,76,97,98,94,93,107,99,101,93,97,97,79,107,102,98,115,102,104,156,101,80", "endOffsets": "213,315,430,519,630,751,830,907,1005,1104,1199,1293,1401,1501,1603,1697,1795,1893,1973,2081,2184,2283,2399,2502,2607,2764,2866,6981"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\59cf662b2b8d845940ff382134f3256f\\transformed\\core-1.13.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,558,673,801", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "146,249,348,446,553,668,796,897"}, "to": {"startLines": "31,32,33,34,35,36,37,65", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3102,3198,3301,3400,3498,3605,3720,6986", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "3193,3296,3395,3493,3600,3715,3843,7082"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2cc8b6019d92a5e15cae9db0f9def0ab\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-ta\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "156", "endOffsets": "351"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4832", "endColumns": "160", "endOffsets": "4988"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f8dec0504ea506eec631ea3375271c98\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,116", "endOffsets": "164,281"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2871,2985", "endColumns": "113,116", "endOffsets": "2980,3097"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ffe8c5202c8aee5e22f6886f6465e53a\\transformed\\preference-1.2.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,264,347,496,665,746", "endColumns": "69,88,82,148,168,80,76", "endOffsets": "170,259,342,491,660,741,818"}, "to": {"startLines": "56,58,62,63,66,67,68", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6084,6271,6673,6756,7087,7256,7337", "endColumns": "69,88,82,148,168,80,76", "endOffsets": "6149,6355,6751,6900,7251,7332,7409"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a0aa6191d710310d82f16cc8f8088c89\\transformed\\jetified-play-services-base-18.5.0\\res\\values-ta\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,442,565,667,815,939,1048,1145,1321,1424,1573,1704,1854,2006,2064,2123", "endColumns": "100,147,122,101,147,123,108,96,175,102,148,130,149,151,57,58,76", "endOffsets": "293,441,564,666,814,938,1047,1144,1320,1423,1572,1703,1853,2005,2063,2122,2199"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3848,3953,4105,4232,4338,4490,4618,4731,4993,5173,5280,5433,5568,5722,5878,5940,6003", "endColumns": "104,151,126,105,151,127,112,100,179,106,152,134,153,155,61,62,80", "endOffsets": "3948,4100,4227,4333,4485,4613,4726,4827,5168,5275,5428,5563,5717,5873,5935,5998,6079"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\47a3d3d808bdabe9e0d6e5500da6f30d\\transformed\\browser-1.8.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,172,274,381", "endColumns": "116,101,106,103", "endOffsets": "167,269,376,480"}, "to": {"startLines": "57,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "6154,6360,6462,6569", "endColumns": "116,101,106,103", "endOffsets": "6266,6457,6564,6668"}}]}]}