import 'package:flutter/material.dart';
import '../../config/app_theme.dart';
import '../../services/mock_services.dart';

class HotelRegistrationScreen extends StatefulWidget {
  final String vendorId;

  const HotelRegistrationScreen({
    super.key,
    required this.vendorId,
  });

  @override
  State<HotelRegistrationScreen> createState() =>
      _HotelRegistrationScreenState();
}

class _HotelRegistrationScreenState extends State<HotelRegistrationScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _formKey = GlobalKey<FormState>();
  final _mockService = MockHotelRegistrationService();

  // Form Controllers
  final _hotelNameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _streetController = TextEditingController();
  final _cityController = TextEditingController();
  final _stateController = TextEditingController();
  final _pincodeController = TextEditingController();
  final _landmarkController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _websiteController = TextEditingController();
  final _checkInTimeController = TextEditingController();
  final _checkOutTimeController = TextEditingController();
  final _cancellationPolicyController = TextEditingController();

  // Form Data
  HotelType _selectedHotelType = HotelType.hotel;
  int _starRating = 3;
  List<String> _selectedAmenities = [];
  List<RoomType> _roomTypes = [];
  bool _petsAllowed = false;
  bool _smokingAllowed = false;
  int _minimumAge = 18;
  bool _isLoading = false;

  // Available amenities
  final List<String> _availableAmenities = [
    'WiFi',
    'Parking',
    'Swimming Pool',
    'Gym/Fitness Center',
    'Spa',
    'Restaurant',
    'Room Service',
    'Laundry Service',
    'Airport Shuttle',
    'Business Center',
    'Conference Rooms',
    'Pet Friendly',
    'Air Conditioning',
    'Elevator',
    'Balcony/Terrace',
    'Garden',
    'Bar/Lounge',
    'Concierge',
    'Valet Parking',
    '24/7 Front Desk',
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _checkInTimeController.text = '14:00';
    _checkOutTimeController.text = '11:00';
    _cancellationPolicyController.text =
        'Free cancellation up to 24 hours before check-in';
  }

  @override
  void dispose() {
    _tabController.dispose();
    _hotelNameController.dispose();
    _descriptionController.dispose();
    _streetController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _pincodeController.dispose();
    _landmarkController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _websiteController.dispose();
    _checkInTimeController.dispose();
    _checkOutTimeController.dispose();
    _cancellationPolicyController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Register New Hotel'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'Basic Info'),
            Tab(text: 'Location'),
            Tab(text: 'Amenities'),
            Tab(text: 'Policies'),
          ],
        ),
      ),
      body: Form(
        key: _formKey,
        child: TabBarView(
          controller: _tabController,
          children: [
            _buildBasicInfoTab(),
            _buildLocationTab(),
            _buildAmenitiesTab(),
            _buildPoliciesTab(),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: Row(
          children: [
            if (_tabController.index > 0)
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    _tabController.animateTo(_tabController.index - 1);
                  },
                  child: const Text('Previous'),
                ),
              ),
            if (_tabController.index > 0) const SizedBox(width: 16),
            Expanded(
              child: ElevatedButton(
                onPressed: _isLoading ? null : _handleNextOrSubmit,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: _isLoading
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(_tabController.index == 3
                        ? 'Submit Application'
                        : 'Next'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Hotel Basic Information',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          TextFormField(
            controller: _hotelNameController,
            decoration: const InputDecoration(
              labelText: 'Hotel Name *',
              hintText: 'Enter your hotel name',
              border: OutlineInputBorder(),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter hotel name';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _descriptionController,
            decoration: const InputDecoration(
              labelText: 'Hotel Description *',
              hintText: 'Describe your hotel, facilities, and unique features',
              border: OutlineInputBorder(),
            ),
            maxLines: 4,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter hotel description';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          const Text(
            'Hotel Type',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          DropdownButtonFormField<HotelType>(
            value: _selectedHotelType,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
            ),
            items: HotelType.values.map((type) {
              return DropdownMenuItem(
                value: type,
                child: Text(_getHotelTypeDisplayName(type)),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedHotelType = value!;
              });
            },
          ),
          const SizedBox(height: 16),
          const Text(
            'Star Rating',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: List.generate(5, (index) {
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _starRating = index + 1;
                  });
                },
                child: Icon(
                  index < _starRating ? Icons.star : Icons.star_border,
                  color: Colors.amber,
                  size: 32,
                ),
              );
            }),
          ),
          const SizedBox(height: 16),
          const Text(
            'Contact Information',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          TextFormField(
            controller: _emailController,
            decoration: const InputDecoration(
              labelText: 'Hotel Email *',
              hintText: '<EMAIL>',
              border: OutlineInputBorder(),
            ),
            keyboardType: TextInputType.emailAddress,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter hotel email';
              }
              if (!value.contains('@')) {
                return 'Please enter a valid email';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _phoneController,
            decoration: const InputDecoration(
              labelText: 'Hotel Phone *',
              hintText: '+91 XXXXXXXXXX',
              border: OutlineInputBorder(),
            ),
            keyboardType: TextInputType.phone,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter hotel phone';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _websiteController,
            decoration: const InputDecoration(
              labelText: 'Hotel Website (Optional)',
              hintText: 'https://www.yourhotel.com',
              border: OutlineInputBorder(),
            ),
            keyboardType: TextInputType.url,
          ),
        ],
      ),
    );
  }

  Widget _buildLocationTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Hotel Location',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          TextFormField(
            controller: _streetController,
            decoration: const InputDecoration(
              labelText: 'Street Address *',
              hintText: 'Enter complete street address',
              border: OutlineInputBorder(),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter street address';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _cityController,
                  decoration: const InputDecoration(
                    labelText: 'City *',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter city';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  controller: _stateController,
                  decoration: const InputDecoration(
                    labelText: 'State *',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter state';
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _pincodeController,
            decoration: const InputDecoration(
              labelText: 'Pincode *',
              border: OutlineInputBorder(),
            ),
            keyboardType: TextInputType.number,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter pincode';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _landmarkController,
            decoration: const InputDecoration(
              labelText: 'Landmark (Optional)',
              hintText: 'Near famous landmark or location',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 20),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue[200]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.info, color: Colors.blue[600]),
                    const SizedBox(width: 8),
                    Text(
                      'Location Tips',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue[600],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                const Text(
                  '• Provide accurate address for better customer experience\n'
                  '• Include nearby landmarks for easy navigation\n'
                  '• Ensure the location is accessible by public transport',
                  style: TextStyle(fontSize: 14),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAmenitiesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Hotel Amenities',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Select all amenities available at your hotel',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 20),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _availableAmenities.map((amenity) {
              final isSelected = _selectedAmenities.contains(amenity);
              return FilterChip(
                label: Text(amenity),
                selected: isSelected,
                onSelected: (selected) {
                  setState(() {
                    if (selected) {
                      _selectedAmenities.add(amenity);
                    } else {
                      _selectedAmenities.remove(amenity);
                    }
                  });
                },
                backgroundColor: Colors.grey[200],
                selectedColor: AppTheme.primaryColor.withOpacity(0.2),
                checkmarkColor: AppTheme.primaryColor,
              );
            }).toList(),
          ),
          const SizedBox(height: 30),
          const Text(
            'Room Types',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ..._roomTypes.asMap().entries.map((entry) {
            final index = entry.key;
            final roomType = entry.value;
            return _buildRoomTypeCard(roomType, index);
          }).toList(),
          const SizedBox(height: 16),
          OutlinedButton.icon(
            onPressed: _addRoomType,
            icon: const Icon(Icons.add),
            label: const Text('Add Room Type'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppTheme.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPoliciesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Hotel Policies',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _checkInTimeController,
                  decoration: const InputDecoration(
                    labelText: 'Check-in Time *',
                    hintText: '14:00',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter check-in time';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  controller: _checkOutTimeController,
                  decoration: const InputDecoration(
                    labelText: 'Check-out Time *',
                    hintText: '11:00',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter check-out time';
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _cancellationPolicyController,
            decoration: const InputDecoration(
              labelText: 'Cancellation Policy *',
              border: OutlineInputBorder(),
            ),
            maxLines: 3,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter cancellation policy';
              }
              return null;
            },
          ),
          const SizedBox(height: 20),
          SwitchListTile(
            title: const Text('Pets Allowed'),
            subtitle: const Text('Allow guests to bring pets'),
            value: _petsAllowed,
            onChanged: (value) {
              setState(() {
                _petsAllowed = value;
              });
            },
            activeColor: AppTheme.primaryColor,
          ),
          SwitchListTile(
            title: const Text('Smoking Allowed'),
            subtitle: const Text('Allow smoking in designated areas'),
            value: _smokingAllowed,
            onChanged: (value) {
              setState(() {
                _smokingAllowed = value;
              });
            },
            activeColor: AppTheme.primaryColor,
          ),
          const SizedBox(height: 16),
          TextFormField(
            decoration: const InputDecoration(
              labelText: 'Minimum Age for Check-in',
              border: OutlineInputBorder(),
            ),
            keyboardType: TextInputType.number,
            initialValue: _minimumAge.toString(),
            onChanged: (value) {
              _minimumAge = int.tryParse(value) ?? 18;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildRoomTypeCard(RoomType roomType, int index) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  roomType.name,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  onPressed: () => _removeRoomType(index),
                  icon: const Icon(Icons.delete, color: Colors.red),
                ),
              ],
            ),
            Text('Max Occupancy: ${roomType.maxOccupancy}'),
            Text('Base Price: ₹${roomType.basePrice.toStringAsFixed(0)}'),
            Text('Total Rooms: ${roomType.totalRooms}'),
            if (roomType.amenities.isNotEmpty)
              Text('Amenities: ${roomType.amenities.join(', ')}'),
          ],
        ),
      ),
    );
  }

  void _addRoomType() {
    showDialog(
      context: context,
      builder: (context) => _RoomTypeDialog(
        onSave: (roomType) {
          setState(() {
            _roomTypes.add(roomType);
          });
        },
      ),
    );
  }

  void _removeRoomType(int index) {
    setState(() {
      _roomTypes.removeAt(index);
    });
  }

  String _getHotelTypeDisplayName(HotelType type) {
    switch (type) {
      case HotelType.hotel:
        return 'Hotel';
      case HotelType.resort:
        return 'Resort';
      case HotelType.guesthouse:
        return 'Guest House';
      case HotelType.hostel:
        return 'Hostel';
      case HotelType.apartment:
        return 'Apartment';
      case HotelType.villa:
        return 'Villa';
      case HotelType.homestay:
        return 'Homestay';
    }
  }

  void _handleNextOrSubmit() {
    if (_tabController.index < 3) {
      // Validate current tab
      if (_validateCurrentTab()) {
        _tabController.animateTo(_tabController.index + 1);
      }
    } else {
      // Submit form
      _submitHotelRegistration();
    }
  }

  bool _validateCurrentTab() {
    switch (_tabController.index) {
      case 0:
        return _hotelNameController.text.isNotEmpty &&
            _descriptionController.text.isNotEmpty &&
            _emailController.text.isNotEmpty &&
            _phoneController.text.isNotEmpty;
      case 1:
        return _streetController.text.isNotEmpty &&
            _cityController.text.isNotEmpty &&
            _stateController.text.isNotEmpty &&
            _pincodeController.text.isNotEmpty;
      case 2:
        return _roomTypes.isNotEmpty;
      case 3:
        return _checkInTimeController.text.isNotEmpty &&
            _checkOutTimeController.text.isNotEmpty &&
            _cancellationPolicyController.text.isNotEmpty;
      default:
        return true;
    }
  }

  Future<void> _submitHotelRegistration() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final hotel = HotelRegistration(
        id: '',
        vendorId: widget.vendorId,
        hotelName: _hotelNameController.text,
        description: _descriptionController.text,
        address: HotelAddress(
          street: _streetController.text,
          city: _cityController.text,
          state: _stateController.text,
          pincode: _pincodeController.text,
          country: 'India',
          landmark: _landmarkController.text.isNotEmpty
              ? _landmarkController.text
              : null,
        ),
        amenities: _selectedAmenities,
        images: [], // Will be uploaded separately
        hotelType: _selectedHotelType,
        starRating: _starRating,
        roomTypes: _roomTypes,
        policies: HotelPolicies(
          checkInTime: _checkInTimeController.text,
          checkOutTime: _checkOutTimeController.text,
          cancellationPolicy: _cancellationPolicyController.text,
          petsAllowed: _petsAllowed,
          smokingAllowed: _smokingAllowed,
          minimumAge: _minimumAge,
        ),
        contactInfo: ContactInfo(
          email: _emailController.text,
          phone: _phoneController.text,
          website: _websiteController.text.isNotEmpty
              ? _websiteController.text
              : null,
        ),
        status: HotelStatus.pending,
        createdAt: DateTime.now(),
      );

      // Simulate hotel registration
      await Future.delayed(const Duration(seconds: 2));
      final hotelId = 'mock_hotel_id';

      if (hotelId != null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Hotel registration submitted successfully!'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context);
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to submit hotel registration'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}

// Room Type Dialog
class _RoomTypeDialog extends StatefulWidget {
  final Function(RoomType) onSave;

  const _RoomTypeDialog({required this.onSave});

  @override
  State<_RoomTypeDialog> createState() => _RoomTypeDialogState();
}

class _RoomTypeDialogState extends State<_RoomTypeDialog> {
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _roomCountController = TextEditingController();
  int _maxOccupancy = 2;
  List<String> _selectedAmenities = [];

  final List<String> _roomAmenities = [
    'Air Conditioning',
    'WiFi',
    'TV',
    'Mini Bar',
    'Balcony',
    'Sea View',
    'City View',
    'Jacuzzi',
    'Work Desk',
    'Safe',
    'Coffee Maker',
    'Room Service',
  ];

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Add Room Type'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Room Type Name',
                hintText: 'e.g., Deluxe Room, Suite',
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description',
              ),
              maxLines: 2,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _priceController,
                    decoration: const InputDecoration(
                      labelText: 'Base Price (₹)',
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextField(
                    controller: _roomCountController,
                    decoration: const InputDecoration(
                      labelText: 'Total Rooms',
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                const Text('Max Occupancy: '),
                DropdownButton<int>(
                  value: _maxOccupancy,
                  items: [1, 2, 3, 4, 5, 6].map((count) {
                    return DropdownMenuItem(
                      value: count,
                      child: Text('$count'),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _maxOccupancy = value!;
                    });
                  },
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text('Room Amenities:'),
            Wrap(
              spacing: 8,
              children: _roomAmenities.map((amenity) {
                final isSelected = _selectedAmenities.contains(amenity);
                return FilterChip(
                  label: Text(amenity),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      if (selected) {
                        _selectedAmenities.add(amenity);
                      } else {
                        _selectedAmenities.remove(amenity);
                      }
                    });
                  },
                );
              }).toList(),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_nameController.text.isNotEmpty &&
                _priceController.text.isNotEmpty &&
                _roomCountController.text.isNotEmpty) {
              final roomType = RoomType(
                name: _nameController.text,
                description: _descriptionController.text,
                maxOccupancy: _maxOccupancy,
                basePrice: double.parse(_priceController.text),
                amenities: _selectedAmenities,
                totalRooms: int.parse(_roomCountController.text),
              );
              widget.onSave(roomType);
              Navigator.pop(context);
            }
          },
          child: const Text('Add'),
        ),
      ],
    );
  }
}
