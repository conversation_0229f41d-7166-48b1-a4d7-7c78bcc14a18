{"logs": [{"outputFile": "com.example.hotel_vendor_app-mergeReleaseResources-51:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a0aa6191d710310d82f16cc8f8088c89\\transformed\\jetified-play-services-base-18.5.0\\res\\values-pt-rPT\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,457,581,685,849,973,1091,1196,1380,1484,1650,1777,1932,2106,2170,2235", "endColumns": "100,158,123,103,163,123,117,104,183,103,165,126,154,173,63,64,82", "endOffsets": "297,456,580,684,848,972,1090,1195,1379,1483,1649,1776,1931,2105,2169,2234,2317"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3819,3924,4087,4215,4323,4491,4619,4741,4995,5183,5291,5461,5592,5751,5929,5997,6066", "endColumns": "104,162,127,107,167,127,121,108,187,107,169,130,158,177,67,68,86", "endOffsets": "3919,4082,4210,4318,4486,4614,4736,4845,5178,5286,5456,5587,5746,5924,5992,6061,6148"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2cc8b6019d92a5e15cae9db0f9def0ab\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-pt-rPT\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "140", "endOffsets": "339"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4850", "endColumns": "144", "endOffsets": "4990"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f8dec0504ea506eec631ea3375271c98\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,172", "endColumns": "116,121", "endOffsets": "167,289"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2848,2965", "endColumns": "116,121", "endOffsets": "2960,3082"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\772e2867940e4356072472f7dc271176\\transformed\\appcompat-1.1.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,740,825,906,998,1091,1188,1282,1382,1476,1572,1667,1759,1851,1935,2042,2153,2255,2363,2471,2578,2749,2848", "endColumns": "107,105,106,88,100,123,84,80,91,92,96,93,99,93,95,94,91,91,83,106,110,101,107,107,106,170,98,84", "endOffsets": "208,314,421,510,611,735,820,901,993,1086,1183,1277,1377,1471,1567,1662,1754,1846,1930,2037,2148,2250,2358,2466,2573,2744,2843,2928"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,740,825,906,998,1091,1188,1282,1382,1476,1572,1667,1759,1851,1935,2042,2153,2255,2363,2471,2578,2749,6971", "endColumns": "107,105,106,88,100,123,84,80,91,92,96,93,99,93,95,94,91,91,83,106,110,101,107,107,106,170,98,84", "endOffsets": "208,314,421,510,611,735,820,901,993,1086,1183,1277,1377,1471,1567,1662,1754,1846,1930,2037,2148,2250,2358,2466,2573,2744,2843,7051"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ffe8c5202c8aee5e22f6886f6465e53a\\transformed\\preference-1.2.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,263,342,493,662,749", "endColumns": "69,87,78,150,168,86,80", "endOffsets": "170,258,337,488,657,744,825"}, "to": {"startLines": "56,58,62,63,66,67,68", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6153,6339,6741,6820,7157,7326,7413", "endColumns": "69,87,78,150,168,86,80", "endOffsets": "6218,6422,6815,6966,7321,7408,7489"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\47a3d3d808bdabe9e0d6e5500da6f30d\\transformed\\browser-1.8.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,171,270,382", "endColumns": "115,98,111,102", "endOffsets": "166,265,377,480"}, "to": {"startLines": "57,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "6223,6427,6526,6638", "endColumns": "115,98,111,102", "endOffsets": "6334,6521,6633,6736"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\59cf662b2b8d845940ff382134f3256f\\transformed\\core-1.13.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,787", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,883"}, "to": {"startLines": "31,32,33,34,35,36,37,65", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3087,3184,3286,3385,3485,3592,3698,7056", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "3179,3281,3380,3480,3587,3693,3814,7152"}}]}]}