import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'hybrid_auth_service.dart';

/// Firebase Firestore service for guests management
class FirebaseGuestsService {
  static final FirebaseGuestsService _instance = FirebaseGuestsService._internal();
  factory FirebaseGuestsService() => _instance;
  FirebaseGuestsService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final HybridAuthService _authService = HybridAuthService();

  /// Get current user ID
  String? get _currentUserId => _authService.currentUser?['id'];

  /// Get user's guests collection reference
  CollectionReference get _guestsCollection {
    if (_currentUserId == null) {
      throw Exception('User not authenticated');
    }
    return _firestore.collection('vendors').doc(_currentUserId).collection('guests');
  }

  /// Get all guests for current user
  Future<List<Map<String, dynamic>>> getUserGuests() async {
    try {
      if (_currentUserId == null) return [];

      final querySnapshot = await _guestsCollection.orderBy('createdAt', descending: true).get();
      return querySnapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      if (kDebugMode) print('Error getting user guests: $e');
      return [];
    }
  }

  /// Get guests by verification status
  Future<List<Map<String, dynamic>>> getGuestsByVerificationStatus(bool isVerified) async {
    try {
      if (_currentUserId == null) return [];

      final querySnapshot = await _guestsCollection
          .where('aadharVerified', isEqualTo: isVerified)
          .orderBy('createdAt', descending: true)
          .get();
      
      return querySnapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      if (kDebugMode) print('Error getting guests by verification status: $e');
      return [];
    }
  }

  /// Get currently checked-in guests
  Future<List<Map<String, dynamic>>> getCheckedInGuests() async {
    try {
      if (_currentUserId == null) return [];

      final querySnapshot = await _guestsCollection
          .where('status', isEqualTo: 'Checked-In')
          .orderBy('createdAt', descending: true)
          .get();
      
      return querySnapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      if (kDebugMode) print('Error getting checked-in guests: $e');
      return [];
    }
  }

  /// Add new guest
  Future<bool> addGuest(Map<String, dynamic> guestData) async {
    try {
      if (_currentUserId == null) return false;

      final newGuestData = {
        ...guestData,
        'vendorId': _currentUserId,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      };

      await _guestsCollection.add(newGuestData);
      
      if (kDebugMode) print('Guest added successfully to Firestore');
      return true;
    } catch (e) {
      if (kDebugMode) print('Error adding guest to Firestore: $e');
      return false;
    }
  }

  /// Update guest
  Future<bool> updateGuest(String guestId, Map<String, dynamic> updates) async {
    try {
      if (_currentUserId == null) return false;

      final updateData = {
        ...updates,
        'updatedAt': FieldValue.serverTimestamp(),
      };

      await _guestsCollection.doc(guestId).update(updateData);
      
      if (kDebugMode) print('Guest updated successfully in Firestore');
      return true;
    } catch (e) {
      if (kDebugMode) print('Error updating guest in Firestore: $e');
      return false;
    }
  }

  /// Delete guest
  Future<bool> deleteGuest(String guestId) async {
    try {
      if (_currentUserId == null) return false;

      await _guestsCollection.doc(guestId).delete();
      
      if (kDebugMode) print('Guest deleted successfully from Firestore');
      return true;
    } catch (e) {
      if (kDebugMode) print('Error deleting guest from Firestore: $e');
      return false;
    }
  }

  /// Get guest by ID
  Future<Map<String, dynamic>?> getGuestById(String guestId) async {
    try {
      if (_currentUserId == null) return null;

      final doc = await _guestsCollection.doc(guestId).get();
      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return data;
      }
      return null;
    } catch (e) {
      if (kDebugMode) print('Error getting guest by ID: $e');
      return null;
    }
  }

  /// Update guest verification status
  Future<bool> updateGuestVerification(String guestId, bool isVerified) async {
    return await updateGuest(guestId, {
      'aadharVerified': isVerified,
      'verifiedAt': isVerified ? FieldValue.serverTimestamp() : null,
    });
  }

  /// Update guest check-in status
  Future<bool> updateGuestCheckInStatus(String guestId, String status) async {
    return await updateGuest(guestId, {
      'status': status,
      'checkInTime': status == 'Checked-In' ? FieldValue.serverTimestamp() : null,
      'checkOutTime': status == 'Checked-Out' ? FieldValue.serverTimestamp() : null,
    });
  }

  /// Get guest statistics
  Future<Map<String, dynamic>> getGuestStatistics() async {
    try {
      if (_currentUserId == null) {
        return {
          'total': 0,
          'checkedIn': 0,
          'checkedOut': 0,
          'verified': 0,
          'unverified': 0,
          'pending': 0,
        };
      }

      final guests = await getUserGuests();
      
      final checkedIn = guests.where((g) => g['status']?.toLowerCase() == 'checked-in').length;
      final checkedOut = guests.where((g) => g['status']?.toLowerCase() == 'checked-out').length;
      final verified = guests.where((g) => g['aadharVerified'] == true).length;
      final unverified = guests.where((g) => g['aadharVerified'] == false).length;
      final pending = guests.where((g) => g['status']?.toLowerCase() == 'pending').length;
      
      return {
        'total': guests.length,
        'checkedIn': checkedIn,
        'checkedOut': checkedOut,
        'verified': verified,
        'unverified': unverified,
        'pending': pending,
      };
    } catch (e) {
      if (kDebugMode) print('Error getting guest statistics: $e');
      return {
        'total': 0,
        'checkedIn': 0,
        'checkedOut': 0,
        'verified': 0,
        'unverified': 0,
        'pending': 0,
      };
    }
  }

  /// Search guests
  Future<List<Map<String, dynamic>>> searchGuests(String searchTerm) async {
    try {
      if (_currentUserId == null) return [];

      // Firestore doesn't support full-text search, so we'll get all guests and filter locally
      final guests = await getUserGuests();
      final searchLower = searchTerm.toLowerCase();
      
      return guests.where((guest) {
        final name = (guest['name'] ?? '').toString().toLowerCase();
        final email = (guest['email'] ?? '').toString().toLowerCase();
        final phone = (guest['phone'] ?? '').toString().toLowerCase();
        final roomNumber = (guest['roomNumber'] ?? '').toString().toLowerCase();
        final aadharNumber = (guest['aadharNumber'] ?? '').toString().toLowerCase();
        
        return name.contains(searchLower) ||
               email.contains(searchLower) ||
               phone.contains(searchLower) ||
               roomNumber.contains(searchLower) ||
               aadharNumber.contains(searchLower);
      }).toList();
    } catch (e) {
      if (kDebugMode) print('Error searching guests: $e');
      return [];
    }
  }

  /// Initialize user with sample guests (only for new users)
  Future<void> initializeNewUserGuests() async {
    try {
      if (_currentUserId == null) return;

      final existingGuests = await getUserGuests();
      if (existingGuests.isNotEmpty) return; // User already has guests

      if (kDebugMode) print('Initializing sample guests in Firestore for new user');

      final now = DateTime.now();
      
      // Add some initial guests for demonstration
      final sampleGuests = [
        {
          'name': 'John Doe',
          'email': '<EMAIL>',
          'phone': '+91 9876543210',
          'aadharNumber': '1234-5678-9012',
          'address': '123 Main Street, City, State',
          'roomNumber': '101',
          'checkInDate': Timestamp.fromDate(now.add(const Duration(days: 1))),
          'checkOutDate': Timestamp.fromDate(now.add(const Duration(days: 3))),
          'numberOfGuests': 2,
          'status': 'Confirmed',
          'aadharVerified': false,
          'specialRequests': 'Late check-in requested',
          'emergencyContact': '+91 9876543213',
        },
        {
          'name': 'Jane Smith',
          'email': '<EMAIL>',
          'phone': '+91 9876543211',
          'aadharNumber': '**************',
          'address': '456 Oak Avenue, City, State',
          'roomNumber': '102',
          'checkInDate': Timestamp.fromDate(now.subtract(const Duration(days: 1))),
          'checkOutDate': Timestamp.fromDate(now.add(const Duration(days: 1))),
          'numberOfGuests': 1,
          'status': 'Checked-In',
          'aadharVerified': true,
          'verifiedAt': Timestamp.fromDate(now.subtract(const Duration(hours: 2))),
          'checkInTime': Timestamp.fromDate(now.subtract(const Duration(hours: 2))),
          'specialRequests': '',
          'emergencyContact': '+91 9876543214',
        },
        {
          'name': 'Mike Johnson',
          'email': '<EMAIL>',
          'phone': '+91 9876543212',
          'aadharNumber': '**************',
          'address': '789 Pine Road, City, State',
          'roomNumber': '201',
          'checkInDate': Timestamp.fromDate(now.add(const Duration(days: 5))),
          'checkOutDate': Timestamp.fromDate(now.add(const Duration(days: 8))),
          'numberOfGuests': 4,
          'status': 'Pending',
          'aadharVerified': false,
          'specialRequests': 'Airport pickup required',
          'emergencyContact': '+91 9876543215',
        },
      ];

      // Add guests to Firestore
      for (final guest in sampleGuests) {
        await addGuest(guest);
      }

      if (kDebugMode) print('Sample guests initialized successfully in Firestore');
    } catch (e) {
      if (kDebugMode) print('Error initializing sample guests: $e');
    }
  }

  /// Get guest status options
  List<String> getGuestStatusOptions() {
    return [
      'Pending',
      'Confirmed',
      'Checked-In',
      'Checked-Out',
      'Cancelled',
      'No-Show',
    ];
  }

  /// Stream guests for real-time updates
  Stream<List<Map<String, dynamic>>> streamUserGuests() {
    if (_currentUserId == null) {
      return Stream.value([]);
    }

    return _guestsCollection.orderBy('createdAt', descending: true).snapshots().map((snapshot) {
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return data;
      }).toList();
    });
  }

  /// Clear all user guest data (for logout)
  Future<void> clearUserGuestData() async {
    // For Firestore, we don't need to clear data on logout
    // Data remains in the cloud and will be available on next login
    if (kDebugMode) print('Guest data remains in Firestore for next login');
  }
}
