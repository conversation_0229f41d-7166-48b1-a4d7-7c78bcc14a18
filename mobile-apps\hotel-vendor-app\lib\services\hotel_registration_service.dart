import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'dart:io';
import '../models/vendor_models.dart';

class HotelRegistrationService {
  static final HotelRegistrationService _instance = HotelRegistrationService._internal();
  factory HotelRegistrationService() => _instance;
  HotelRegistrationService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;

  // Collections
  static const String vendorsCollection = 'hotel_vendors';
  static const String hotelsCollection = 'hotel_registrations';
  static const String documentsCollection = 'vendor_documents';

  /// Register a new hotel vendor
  Future<String?> registerVendor(HotelVendor vendor) async {
    try {
      final docRef = await _firestore
          .collection(vendorsCollection)
          .add(vendor.toFirestore());
      
      return docRef.id;
    } catch (e) {
      print('Error registering vendor: $e');
      return null;
    }
  }

  /// Get vendor by ID
  Future<HotelVendor?> getVendor(String vendorId) async {
    try {
      final doc = await _firestore
          .collection(vendorsCollection)
          .doc(vendorId)
          .get();
      
      if (doc.exists) {
        return HotelVendor.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      print('Error getting vendor: $e');
      return null;
    }
  }

  /// Get vendor by email
  Future<HotelVendor?> getVendorByEmail(String email) async {
    try {
      final query = await _firestore
          .collection(vendorsCollection)
          .where('email', isEqualTo: email)
          .limit(1)
          .get();
      
      if (query.docs.isNotEmpty) {
        return HotelVendor.fromFirestore(query.docs.first);
      }
      return null;
    } catch (e) {
      print('Error getting vendor by email: $e');
      return null;
    }
  }

  /// Update vendor status
  Future<bool> updateVendorStatus(String vendorId, VendorStatus status) async {
    try {
      await _firestore
          .collection(vendorsCollection)
          .doc(vendorId)
          .update({
        'status': status.toString().split('.').last,
        if (status == VendorStatus.verified) 'verifiedAt': Timestamp.now(),
      });
      return true;
    } catch (e) {
      print('Error updating vendor status: $e');
      return false;
    }
  }

  /// Register a new hotel
  Future<String?> registerHotel(HotelRegistration hotel) async {
    try {
      final docRef = await _firestore
          .collection(hotelsCollection)
          .add(hotel.toFirestore());
      
      // Add hotel ID to vendor's hotel list
      await _firestore
          .collection(vendorsCollection)
          .doc(hotel.vendorId)
          .update({
        'hotelIds': FieldValue.arrayUnion([docRef.id]),
      });
      
      return docRef.id;
    } catch (e) {
      print('Error registering hotel: $e');
      return null;
    }
  }

  /// Get hotels by vendor ID
  Future<List<HotelRegistration>> getVendorHotels(String vendorId) async {
    try {
      final query = await _firestore
          .collection(hotelsCollection)
          .where('vendorId', isEqualTo: vendorId)
          .get();
      
      return query.docs
          .map((doc) => HotelRegistration.fromFirestore(doc))
          .toList();
    } catch (e) {
      print('Error getting vendor hotels: $e');
      return [];
    }
  }

  /// Update hotel status
  Future<bool> updateHotelStatus(String hotelId, HotelStatus status) async {
    try {
      await _firestore
          .collection(hotelsCollection)
          .doc(hotelId)
          .update({
        'status': status.toString().split('.').last,
        if (status == HotelStatus.approved) 'approvedAt': Timestamp.now(),
      });
      return true;
    } catch (e) {
      print('Error updating hotel status: $e');
      return false;
    }
  }

  /// Upload vendor document
  Future<String?> uploadDocument(String vendorId, String documentType, File file) async {
    try {
      final ref = _storage
          .ref()
          .child('vendor_documents')
          .child(vendorId)
          .child('$documentType.${file.path.split('.').last}');
      
      final uploadTask = await ref.putFile(file);
      final downloadUrl = await uploadTask.ref.getDownloadURL();
      
      // Save document info to Firestore
      await _firestore
          .collection(documentsCollection)
          .doc(vendorId)
          .set({
        documentType: {
          'url': downloadUrl,
          'uploadedAt': Timestamp.now(),
          'status': 'pending_verification',
        }
      }, SetOptions(merge: true));
      
      return downloadUrl;
    } catch (e) {
      print('Error uploading document: $e');
      return null;
    }
  }

  /// Upload hotel images
  Future<List<String>> uploadHotelImages(String hotelId, List<File> images) async {
    final uploadedUrls = <String>[];
    
    try {
      for (int i = 0; i < images.length; i++) {
        final file = images[i];
        final ref = _storage
            .ref()
            .child('hotel_images')
            .child(hotelId)
            .child('image_$i.${file.path.split('.').last}');
        
        final uploadTask = await ref.putFile(file);
        final downloadUrl = await uploadTask.ref.getDownloadURL();
        uploadedUrls.add(downloadUrl);
      }
    } catch (e) {
      print('Error uploading hotel images: $e');
    }
    
    return uploadedUrls;
  }

  /// Get pending vendor applications (for admin)
  Future<List<HotelVendor>> getPendingVendors() async {
    try {
      final query = await _firestore
          .collection(vendorsCollection)
          .where('status', isEqualTo: 'pending')
          .orderBy('createdAt', descending: true)
          .get();
      
      return query.docs
          .map((doc) => HotelVendor.fromFirestore(doc))
          .toList();
    } catch (e) {
      print('Error getting pending vendors: $e');
      return [];
    }
  }

  /// Get pending hotel applications (for admin)
  Future<List<HotelRegistration>> getPendingHotels() async {
    try {
      final query = await _firestore
          .collection(hotelsCollection)
          .where('status', isEqualTo: 'pending')
          .orderBy('createdAt', descending: true)
          .get();
      
      return query.docs
          .map((doc) => HotelRegistration.fromFirestore(doc))
          .toList();
    } catch (e) {
      print('Error getting pending hotels: $e');
      return [];
    }
  }

  /// Search hotels by city (for customer app integration)
  Future<List<HotelRegistration>> searchHotelsByCity(String city) async {
    try {
      final query = await _firestore
          .collection(hotelsCollection)
          .where('status', isEqualTo: 'approved')
          .where('address.city', isEqualTo: city)
          .get();
      
      return query.docs
          .map((doc) => HotelRegistration.fromFirestore(doc))
          .toList();
    } catch (e) {
      print('Error searching hotels by city: $e');
      return [];
    }
  }

  /// Get hotel analytics for vendor
  Future<Map<String, dynamic>> getHotelAnalytics(String hotelId) async {
    try {
      // This would integrate with booking data
      // For now, returning mock data
      return {
        'totalBookings': 45,
        'monthlyRevenue': 125000,
        'occupancyRate': 78.5,
        'averageRating': 4.2,
        'totalReviews': 23,
        'topRoomType': 'Deluxe Room',
        'peakSeason': 'December - February',
      };
    } catch (e) {
      print('Error getting hotel analytics: $e');
      return {};
    }
  }

  /// Update vendor bank details
  Future<bool> updateBankDetails(String vendorId, BankDetails bankDetails) async {
    try {
      await _firestore
          .collection(vendorsCollection)
          .doc(vendorId)
          .update({
        'bankDetails': bankDetails.toMap(),
      });
      return true;
    } catch (e) {
      print('Error updating bank details: $e');
      return false;
    }
  }

  /// Get vendor earnings
  Future<Map<String, dynamic>> getVendorEarnings(String vendorId) async {
    try {
      // This would integrate with payment/booking data
      // For now, returning mock data
      return {
        'totalEarnings': 245000,
        'monthlyEarnings': 45000,
        'pendingPayouts': 12000,
        'lastPayoutDate': '2024-01-15',
        'commissionRate': 12.5,
        'totalBookings': 156,
      };
    } catch (e) {
      print('Error getting vendor earnings: $e');
      return {};
    }
  }
}
