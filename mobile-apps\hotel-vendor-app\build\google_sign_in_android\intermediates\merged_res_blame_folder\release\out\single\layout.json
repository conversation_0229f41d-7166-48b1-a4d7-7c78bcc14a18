[{"merged": "io.flutter.plugins.googlesignin.google_sign_in_android-merged_res-26:/layout/notification_template_part_time.xml", "source": "io.flutter.plugins.googlesignin.google_sign_in_android-core-1.13.1-11:/layout/notification_template_part_time.xml"}, {"merged": "io.flutter.plugins.googlesignin.google_sign_in_android-merged_res-26:/layout/notification_template_part_chronometer.xml", "source": "io.flutter.plugins.googlesignin.google_sign_in_android-core-1.13.1-11:/layout/notification_template_part_chronometer.xml"}, {"merged": "io.flutter.plugins.googlesignin.google_sign_in_android-merged_res-26:/layout/ime_base_split_test_activity.xml", "source": "io.flutter.plugins.googlesignin.google_sign_in_android-core-1.13.1-11:/layout/ime_base_split_test_activity.xml"}, {"merged": "io.flutter.plugins.googlesignin.google_sign_in_android-merged_res-26:/layout/custom_dialog.xml", "source": "io.flutter.plugins.googlesignin.google_sign_in_android-core-1.13.1-11:/layout/custom_dialog.xml"}, {"merged": "io.flutter.plugins.googlesignin.google_sign_in_android-merged_res-26:/layout/ime_secondary_split_test_activity.xml", "source": "io.flutter.plugins.googlesignin.google_sign_in_android-core-1.13.1-11:/layout/ime_secondary_split_test_activity.xml"}]