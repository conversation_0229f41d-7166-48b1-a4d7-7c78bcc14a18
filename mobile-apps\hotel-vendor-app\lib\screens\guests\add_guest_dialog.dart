import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../config/app_theme.dart';
import '../../services/firebase_guests_service.dart';
import '../../services/aadhar_verification_service.dart';
import 'aadhar_otp_verification_dialog.dart';

class AddGuestDialog extends StatefulWidget {
  final VoidCallback? onGuestAdded;

  const AddGuestDialog({super.key, this.onGuestAdded});

  @override
  State<AddGuestDialog> createState() => _AddGuestDialogState();
}

class _AddGuestDialogState extends State<AddGuestDialog> {
  final _formKey = GlobalKey<FormState>();
  final _guestsService = FirebaseGuestsService();
  final _aadharService = AadharVerificationService();

  // Form controllers
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _aadharController = TextEditingController();
  final _addressController = TextEditingController();
  final _roomNumberController = TextEditingController();
  final _emergencyContactController = TextEditingController();
  final _specialRequestsController = TextEditingController();

  DateTime? _checkInDate;
  DateTime? _checkOutDate;
  int _numberOfGuests = 1;
  String _status = 'Pending';
  bool _isLoading = false;
  bool _isVerifyingAadhar = false;
  bool _aadharVerified = false;

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _aadharController.dispose();
    _addressController.dispose();
    _roomNumberController.dispose();
    _emergencyContactController.dispose();
    _specialRequestsController.dispose();
    super.dispose();
  }

  Future<void> _verifyAadhar() async {
    if (_aadharController.text.isEmpty) return;

    // Validate Aadhar format first
    if (!_aadharService.isValidAadharFormat(_aadharController.text)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a valid 12-digit Aadhar number'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isVerifyingAadhar = true;
    });

    try {
      // Show OTP verification dialog
      if (mounted) {
        final result = await showDialog<Map<String, dynamic>>(
          context: context,
          barrierDismissible: false,
          builder: (context) => AadharOtpVerificationDialog(
            aadharNumber: _aadharController.text,
            onVerificationComplete: (data) => Navigator.of(context).pop(data),
          ),
        );

        if (result != null && result['isValid'] == true) {
          setState(() {
            _aadharVerified = true;
          });

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Aadhar verified successfully with OTP!'),
                backgroundColor: Colors.green,
              ),
            );
          }
        } else {
          setState(() {
            _aadharVerified = false;
          });
        }
      }
    } catch (e) {
      setState(() {
        _aadharVerified = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error verifying Aadhar: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isVerifyingAadhar = false;
      });
    }
  }

  Future<void> _saveGuest() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final guestData = {
        'name': _nameController.text.trim(),
        'email': _emailController.text.trim(),
        'phone': _phoneController.text.trim(),
        'aadharNumber':
            _aadharService.extractAadharNumber(_aadharController.text),
        'address': _addressController.text.trim(),
        'roomNumber': _roomNumberController.text.trim(),
        'checkInDate': _checkInDate,
        'checkOutDate': _checkOutDate,
        'numberOfGuests': _numberOfGuests,
        'status': _status,
        'aadharVerified': _aadharVerified,
        'emergencyContact': _emergencyContactController.text.trim(),
        'specialRequests': _specialRequestsController.text.trim(),
        'verifiedAt': _aadharVerified ? DateTime.now() : null,
      };

      final success = await _guestsService.addGuest(guestData);

      if (success) {
        if (mounted) {
          Navigator.of(context).pop();
          widget.onGuestAdded?.call();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Guest added successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to add guest'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error adding guest: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.9,
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            // Header
            Row(
              children: [
                const Icon(Icons.person_add,
                    size: 28, color: AppTheme.primaryColor),
                const SizedBox(width: 12),
                const Text(
                  'Add New Guest',
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const Divider(),

            // Form
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Personal Information
                      const Text(
                        'Personal Information',
                        style: TextStyle(
                            fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 16),

                      TextFormField(
                        controller: _nameController,
                        decoration: const InputDecoration(
                          labelText: 'Full Name *',
                          prefixIcon: Icon(Icons.person),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter guest name';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      TextFormField(
                        controller: _emailController,
                        decoration: const InputDecoration(
                          labelText: 'Email *',
                          prefixIcon: Icon(Icons.email),
                        ),
                        keyboardType: TextInputType.emailAddress,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter email';
                          }
                          if (!value.contains('@')) {
                            return 'Please enter a valid email';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      TextFormField(
                        controller: _phoneController,
                        decoration: const InputDecoration(
                          labelText: 'Phone Number *',
                          prefixIcon: Icon(Icons.phone),
                        ),
                        keyboardType: TextInputType.phone,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly
                        ],
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter phone number';
                          }
                          if (value.length < 10) {
                            return 'Please enter a valid phone number';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Aadhar Verification
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: _aadharController,
                              decoration: InputDecoration(
                                labelText: 'Aadhar Number *',
                                prefixIcon: const Icon(Icons.credit_card),
                                suffixIcon: _aadharVerified
                                    ? const Icon(Icons.verified,
                                        color: Colors.green)
                                    : null,
                              ),
                              keyboardType: TextInputType.number,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                                LengthLimitingTextInputFormatter(12),
                              ],
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return 'Please enter Aadhar number';
                                }
                                if (value.length != 12) {
                                  return 'Aadhar number must be 12 digits';
                                }
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(width: 8),
                          ElevatedButton(
                            onPressed:
                                _isVerifyingAadhar ? null : _verifyAadhar,
                            child: _isVerifyingAadhar
                                ? const SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(
                                        strokeWidth: 2),
                                  )
                                : const Text('Verify'),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      TextFormField(
                        controller: _addressController,
                        decoration: const InputDecoration(
                          labelText: 'Address',
                          prefixIcon: Icon(Icons.location_on),
                        ),
                        maxLines: 2,
                      ),
                      const SizedBox(height: 24),

                      // Booking Information
                      const Text(
                        'Booking Information',
                        style: TextStyle(
                            fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 16),

                      TextFormField(
                        controller: _roomNumberController,
                        decoration: const InputDecoration(
                          labelText: 'Room Number *',
                          prefixIcon: Icon(Icons.hotel),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter room number';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Check-in Date
                      InkWell(
                        onTap: () async {
                          final date = await showDatePicker(
                            context: context,
                            initialDate: DateTime.now(),
                            firstDate: DateTime.now(),
                            lastDate:
                                DateTime.now().add(const Duration(days: 365)),
                          );
                          if (date != null) {
                            setState(() {
                              _checkInDate = date;
                            });
                          }
                        },
                        child: InputDecorator(
                          decoration: const InputDecoration(
                            labelText: 'Check-in Date *',
                            prefixIcon: Icon(Icons.calendar_today),
                          ),
                          child: Text(
                            _checkInDate != null
                                ? '${_checkInDate!.day}/${_checkInDate!.month}/${_checkInDate!.year}'
                                : 'Select check-in date',
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Check-out Date
                      InkWell(
                        onTap: () async {
                          final date = await showDatePicker(
                            context: context,
                            initialDate:
                                _checkInDate?.add(const Duration(days: 1)) ??
                                    DateTime.now().add(const Duration(days: 1)),
                            firstDate: _checkInDate ?? DateTime.now(),
                            lastDate:
                                DateTime.now().add(const Duration(days: 365)),
                          );
                          if (date != null) {
                            setState(() {
                              _checkOutDate = date;
                            });
                          }
                        },
                        child: InputDecorator(
                          decoration: const InputDecoration(
                            labelText: 'Check-out Date *',
                            prefixIcon: Icon(Icons.calendar_today),
                          ),
                          child: Text(
                            _checkOutDate != null
                                ? '${_checkOutDate!.day}/${_checkOutDate!.month}/${_checkOutDate!.year}'
                                : 'Select check-out date',
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Number of Guests
                      Row(
                        children: [
                          const Text('Number of Guests: '),
                          const SizedBox(width: 16),
                          DropdownButton<int>(
                            value: _numberOfGuests,
                            items: List.generate(8, (index) => index + 1)
                                .map((count) => DropdownMenuItem(
                                      value: count,
                                      child: Text('$count'),
                                    ))
                                .toList(),
                            onChanged: (value) {
                              setState(() {
                                _numberOfGuests = value ?? 1;
                              });
                            },
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // Status
                      DropdownButtonFormField<String>(
                        value: _status,
                        decoration: const InputDecoration(
                          labelText: 'Status',
                          prefixIcon: Icon(Icons.info),
                        ),
                        items: [
                          'Pending',
                          'Confirmed',
                          'Checked-In',
                          'Checked-Out',
                          'Cancelled'
                        ]
                            .map((status) => DropdownMenuItem(
                                  value: status,
                                  child: Text(status),
                                ))
                            .toList(),
                        onChanged: (value) {
                          setState(() {
                            _status = value ?? 'Pending';
                          });
                        },
                      ),
                      const SizedBox(height: 16),

                      TextFormField(
                        controller: _emergencyContactController,
                        decoration: const InputDecoration(
                          labelText: 'Emergency Contact',
                          prefixIcon: Icon(Icons.emergency),
                        ),
                        keyboardType: TextInputType.phone,
                      ),
                      const SizedBox(height: 16),

                      TextFormField(
                        controller: _specialRequestsController,
                        decoration: const InputDecoration(
                          labelText: 'Special Requests',
                          prefixIcon: Icon(Icons.note),
                        ),
                        maxLines: 3,
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Action Buttons
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _saveGuest,
                    child: _isLoading
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Text('Add Guest'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
