<variant
    name="release"
    package="com.example.hotel_vendor_app"
    minSdkVersion="23"
    targetSdkVersion="35"
    mergedManifest="E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\app\intermediates\merged_manifest\release\AndroidManifest.xml"
    proguardFiles="E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\app\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.1.0;C:\Users\<USER>\dev\flutter\packages\flutter_tools\gradle\flutter_proguard_rules.pro;proguard-rules.pro"
    partialResultsDir="E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\app\intermediates\lint_vital_partial_results\release\out"
    desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\transforms-3\69da8b2c557a2f50ceccef1faeae9f6f\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <manifestPlaceholders>
    <placeholder
        name="applicationName"
        value="android.app.Application" />
  </manifestPlaceholders>
  <mainArtifact
      classOutputs="E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\app\intermediates\javac\release\classes;E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\app\tmp\kotlin-classes\release;E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\app\kotlinToolingMetadata;E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\app\intermediates\compile_and_runtime_not_namespaced_r_class_jar\release\R.jar"
      applicationId="com.hotelvendor.management"
      generatedSourceFolders="E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\app\generated\ap_generated_sources\release\out"
      generatedResourceFolders="E:\Ongoing\lib\mobile-apps\hotel-vendor-app\build\app\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\transforms-3\69da8b2c557a2f50ceccef1faeae9f6f\transformed\D8BackportedDesugaredMethods.txt">
  </mainArtifact>
</variant>
