import 'package:flutter/material.dart';
import '../../config/app_theme.dart';
import '../../services/firebase_rooms_service.dart';
import '../../services/hybrid_auth_service.dart';
import 'room_form_dialog.dart';

class DynamicRoomsScreen extends StatefulWidget {
  const DynamicRoomsScreen({super.key});

  @override
  State<DynamicRoomsScreen> createState() => _DynamicRoomsScreenState();
}

class _DynamicRoomsScreenState extends State<DynamicRoomsScreen> {
  final _roomsService = FirebaseRoomsService();
  final _authService = HybridAuthService();
  List<Map<String, dynamic>> _rooms = [];
  Map<String, dynamic> _statistics = {};
  bool _isLoading = true;
  String _selectedFilter = 'all';

  @override
  void initState() {
    super.initState();
    _loadRoomsData();
  }

  Future<void> _loadRoomsData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _authService.initialize();
      await _roomsService.initializeNewUserRooms();

      final rooms = await _roomsService.getRoomsByStatus(_selectedFilter);
      final stats = await _roomsService.getRoomStatistics();

      if (mounted) {
        setState(() {
          _rooms = rooms;
          _statistics = stats;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading rooms: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Room Management'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadRoomsData,
          ),
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Statistics Summary
                _buildStatisticsSummary(),

                // Filter Tabs
                _buildFilterTabs(),

                // Rooms List
                Expanded(
                  child: _rooms.isEmpty
                      ? _buildEmptyState()
                      : ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: _rooms.length,
                          itemBuilder: (context, index) {
                            final room = _rooms[index];
                            return _buildRoomCard(room);
                          },
                        ),
                ),
              ],
            ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showAddRoomDialog,
        backgroundColor: AppTheme.primaryColor,
        icon: const Icon(Icons.add, color: Colors.white),
        label: const Text('Add Room', style: TextStyle(color: Colors.white)),
      ),
    );
  }

  Widget _buildStatisticsSummary() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem('Total', '${_statistics['total'] ?? 0}', Colors.white),
          _buildStatItem(
              'Available', '${_statistics['available'] ?? 0}', Colors.green),
          _buildStatItem(
              'Occupied', '${_statistics['occupied'] ?? 0}', Colors.orange),
          _buildStatItem(
              'Maintenance', '${_statistics['maintenance'] ?? 0}', Colors.red),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.white70,
          ),
        ),
      ],
    );
  }

  Widget _buildFilterTabs() {
    final filters = [
      {'key': 'all', 'label': 'All', 'count': _statistics['total'] ?? 0},
      {
        'key': 'available',
        'label': 'Available',
        'count': _statistics['available'] ?? 0
      },
      {
        'key': 'occupied',
        'label': 'Occupied',
        'count': _statistics['occupied'] ?? 0
      },
      {
        'key': 'maintenance',
        'label': 'Maintenance',
        'count': _statistics['maintenance'] ?? 0
      },
    ];

    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: filters.length,
        itemBuilder: (context, index) {
          final filter = filters[index];
          final isSelected = _selectedFilter == filter['key'];

          return GestureDetector(
            onTap: () => _filterRooms(filter['key'] as String),
            child: Container(
              margin: const EdgeInsets.only(right: 12),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color:
                    isSelected ? AppTheme.primaryColor : Colors.grey.shade200,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    filter['label'] as String,
                    style: TextStyle(
                      color: isSelected ? Colors.white : Colors.black87,
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? Colors.white.withOpacity(0.2)
                          : AppTheme.primaryColor,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Text(
                      '${filter['count']}',
                      style: TextStyle(
                        color: isSelected ? Colors.white : Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildRoomCard(Map<String, dynamic> room) {
    final status = room['status'] ?? 'Available';
    final statusColor = AppTheme.getRoomStatusColor(status);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _showRoomDetails(room),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Room Image
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  room['image'] ??
                      'https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=400&h=300&fit=crop',
                  width: 80,
                  height: 80,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      width: 80,
                      height: 80,
                      color: Colors.grey.shade300,
                      child: const Icon(Icons.hotel, color: Colors.grey),
                    );
                  },
                ),
              ),
              const SizedBox(width: 16),

              // Room Details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          'Room ${room['roomNumber'] ?? room['id']}',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const Spacer(),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: statusColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: statusColor),
                          ),
                          child: Text(
                            status,
                            style: TextStyle(
                              color: statusColor,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      room['type'] ?? 'Standard Room',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(Icons.people,
                            size: 16, color: Colors.grey.shade600),
                        const SizedBox(width: 4),
                        Text(
                          '${room['capacity'] ?? 2} guests',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Icon(Icons.currency_rupee,
                            size: 16, color: Colors.grey.shade600),
                        const SizedBox(width: 4),
                        Text(
                          '${room['price'] ?? 0}/night',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Action Button
              PopupMenuButton<String>(
                onSelected: (value) => _handleRoomAction(room, value),
                itemBuilder: (context) => [
                  const PopupMenuItem(value: 'edit', child: Text('Edit Room')),
                  const PopupMenuItem(
                      value: 'status', child: Text('Change Status')),
                  const PopupMenuItem(
                      value: 'delete', child: Text('Delete Room')),
                ],
                child: const Icon(Icons.more_vert),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.hotel_outlined,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            _selectedFilter == 'all'
                ? 'No rooms found'
                : 'No ${_selectedFilter} rooms',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _selectedFilter == 'all'
                ? 'Add your first room to get started'
                : 'Try selecting a different filter',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
          ),
          if (_selectedFilter == 'all') ...[
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _showAddRoomDialog,
              icon: const Icon(Icons.add),
              label: const Text('Add Room'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Future<void> _filterRooms(String filter) async {
    setState(() {
      _selectedFilter = filter;
      _isLoading = true;
    });

    try {
      final rooms = await _roomsService.getRoomsByStatus(filter);
      if (mounted) {
        setState(() {
          _rooms = rooms;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) {
        final searchController = TextEditingController();
        return AlertDialog(
          title: const Text('Search Rooms'),
          content: TextField(
            controller: searchController,
            decoration: const InputDecoration(
              hintText: 'Enter room number, type, or status',
              prefixIcon: Icon(Icons.search),
            ),
            onSubmitted: (value) {
              Navigator.pop(context);
              _performSearch(value);
            },
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                _performSearch(searchController.text);
              },
              child: const Text('Search'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _performSearch(String searchTerm) async {
    if (searchTerm.isEmpty) {
      _loadRoomsData();
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final results = await _roomsService.searchRooms(searchTerm);
      if (mounted) {
        setState(() {
          _rooms = results;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showAddRoomDialog() {
    _showRoomFormDialog();
  }

  void _showRoomDetails(Map<String, dynamic> room) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Room ${room['roomNumber'] ?? room['id']}'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('Type', room['type'] ?? 'N/A'),
              _buildDetailRow('Status', room['status'] ?? 'N/A'),
              _buildDetailRow('Price', '₹${room['price'] ?? 0}/night'),
              _buildDetailRow('Capacity', '${room['capacity'] ?? 0} guests'),
              _buildDetailRow('Floor', '${room['floor'] ?? 'N/A'}'),
              _buildDetailRow('Bed Type', room['bedType'] ?? 'N/A'),
              const SizedBox(height: 16),
              const Text('Amenities:',
                  style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 4,
                children: (room['amenities'] as List<dynamic>? ?? [])
                    .map((amenity) => Chip(
                          label: Text(amenity.toString()),
                          backgroundColor:
                              AppTheme.primaryColor.withOpacity(0.1),
                        ))
                    .toList(),
              ),
              if (room['description'] != null) ...[
                const SizedBox(height: 16),
                const Text('Description:',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 8),
                Text(room['description']),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _showRoomFormDialog(room: room);
            },
            child: const Text('Edit'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _handleRoomAction(Map<String, dynamic> room, String action) {
    switch (action) {
      case 'edit':
        _showRoomFormDialog(room: room);
        break;
      case 'status':
        _showStatusChangeDialog(room);
        break;
      case 'delete':
        _showDeleteConfirmation(room);
        break;
    }
  }

  void _showRoomFormDialog({Map<String, dynamic>? room}) {
    showDialog(
      context: context,
      builder: (context) => RoomFormDialog(
        room: room,
        onSaved: _loadRoomsData,
      ),
    );
  }

  void _showStatusChangeDialog(Map<String, dynamic> room) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Change Status - Room ${room['roomNumber'] ?? room['id']}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: _roomsService
              .getRoomStatusOptions()
              .map((status) => RadioListTile<String>(
                    title: Text(status),
                    value: status,
                    groupValue: room['status'],
                    onChanged: (value) {
                      Navigator.pop(context);
                      _updateRoomStatus(room, value!);
                    },
                  ))
              .toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(Map<String, dynamic> room) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Room'),
        content: Text(
            'Are you sure you want to delete Room ${room['roomNumber'] ?? room['id']}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteRoom(room);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  Future<void> _updateRoomStatus(
      Map<String, dynamic> room, String newStatus) async {
    try {
      final success =
          await _roomsService.updateRoomStatus(room['id'], newStatus);
      if (success) {
        _loadRoomsData();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Room status updated to $newStatus'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to update room status'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating status: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _deleteRoom(Map<String, dynamic> room) async {
    try {
      final success = await _roomsService.deleteRoom(room['id']);
      if (success) {
        _loadRoomsData();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'Room ${room['roomNumber'] ?? room['id']} deleted successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to delete room'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting room: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
