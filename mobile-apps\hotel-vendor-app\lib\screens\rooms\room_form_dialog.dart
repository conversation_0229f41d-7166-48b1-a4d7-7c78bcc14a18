import 'package:flutter/material.dart';
import '../../config/app_theme.dart';
import '../../services/dynamic_rooms_service.dart';

class RoomFormDialog extends StatefulWidget {
  final Map<String, dynamic>? room;
  final VoidCallback? onSaved;

  const RoomFormDialog({
    super.key,
    this.room,
    this.onSaved,
  });

  @override
  State<RoomFormDialog> createState() => _RoomFormDialogState();
}

class _RoomFormDialogState extends State<RoomFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _roomsService = DynamicRoomsService();
  
  // Form controllers
  final _roomNumberController = TextEditingController();
  final _priceController = TextEditingController();
  final _capacityController = TextEditingController();
  final _floorController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _imageController = TextEditingController();
  
  // Form values
  String _selectedType = 'Standard Room';
  String _selectedStatus = 'Available';
  String _selectedBedType = 'King Size';
  List<String> _selectedAmenities = [];
  
  bool _isLoading = false;
  bool get _isEditing => widget.room != null;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    if (_isEditing) {
      final room = widget.room!;
      _roomNumberController.text = room['roomNumber'] ?? '';
      _priceController.text = (room['price'] ?? 0).toString();
      _capacityController.text = (room['capacity'] ?? 2).toString();
      _floorController.text = (room['floor'] ?? 1).toString();
      _descriptionController.text = room['description'] ?? '';
      _imageController.text = room['image'] ?? '';
      
      _selectedType = room['type'] ?? 'Standard Room';
      _selectedStatus = room['status'] ?? 'Available';
      _selectedBedType = room['bedType'] ?? 'King Size';
      _selectedAmenities = List<String>.from(room['amenities'] ?? []);
    }
  }

  @override
  void dispose() {
    _roomNumberController.dispose();
    _priceController.dispose();
    _capacityController.dispose();
    _floorController.dispose();
    _descriptionController.dispose();
    _imageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            // Header
            Row(
              children: [
                Text(
                  _isEditing ? 'Edit Room' : 'Add New Room',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const Divider(),
            
            // Form
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Room Number and Type
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: _roomNumberController,
                              decoration: const InputDecoration(
                                labelText: 'Room Number *',
                                border: OutlineInputBorder(),
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter room number';
                                }
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: DropdownButtonFormField<String>(
                              value: _selectedType,
                              decoration: const InputDecoration(
                                labelText: 'Room Type *',
                                border: OutlineInputBorder(),
                              ),
                              items: _roomsService.getAvailableRoomTypes()
                                  .map((type) => DropdownMenuItem(
                                        value: type,
                                        child: Text(type),
                                      ))
                                  .toList(),
                              onChanged: (value) {
                                setState(() {
                                  _selectedType = value!;
                                });
                              },
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      
                      // Price and Capacity
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: _priceController,
                              decoration: const InputDecoration(
                                labelText: 'Price per Night (₹) *',
                                border: OutlineInputBorder(),
                              ),
                              keyboardType: TextInputType.number,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter price';
                                }
                                if (double.tryParse(value) == null) {
                                  return 'Please enter valid price';
                                }
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: TextFormField(
                              controller: _capacityController,
                              decoration: const InputDecoration(
                                labelText: 'Max Capacity *',
                                border: OutlineInputBorder(),
                              ),
                              keyboardType: TextInputType.number,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter capacity';
                                }
                                if (int.tryParse(value) == null) {
                                  return 'Please enter valid capacity';
                                }
                                return null;
                              },
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      
                      // Floor and Bed Type
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: _floorController,
                              decoration: const InputDecoration(
                                labelText: 'Floor',
                                border: OutlineInputBorder(),
                              ),
                              keyboardType: TextInputType.number,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: DropdownButtonFormField<String>(
                              value: _selectedBedType,
                              decoration: const InputDecoration(
                                labelText: 'Bed Type',
                                border: OutlineInputBorder(),
                              ),
                              items: ['King Size', 'Queen Size', 'Double', 'Single', 'Twin']
                                  .map((type) => DropdownMenuItem(
                                        value: type,
                                        child: Text(type),
                                      ))
                                  .toList(),
                              onChanged: (value) {
                                setState(() {
                                  _selectedBedType = value!;
                                });
                              },
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      
                      // Status
                      DropdownButtonFormField<String>(
                        value: _selectedStatus,
                        decoration: const InputDecoration(
                          labelText: 'Status',
                          border: OutlineInputBorder(),
                        ),
                        items: _roomsService.getRoomStatusOptions()
                            .map((status) => DropdownMenuItem(
                                  value: status,
                                  child: Text(status),
                                ))
                            .toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedStatus = value!;
                          });
                        },
                      ),
                      const SizedBox(height: 16),
                      
                      // Amenities
                      const Text(
                        'Amenities',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                        runSpacing: 4,
                        children: _roomsService.getAvailableAmenities()
                            .map((amenity) => FilterChip(
                                  label: Text(amenity),
                                  selected: _selectedAmenities.contains(amenity),
                                  onSelected: (selected) {
                                    setState(() {
                                      if (selected) {
                                        _selectedAmenities.add(amenity);
                                      } else {
                                        _selectedAmenities.remove(amenity);
                                      }
                                    });
                                  },
                                  selectedColor: AppTheme.primaryColor.withOpacity(0.3),
                                ))
                            .toList(),
                      ),
                      const SizedBox(height: 16),
                      
                      // Description
                      TextFormField(
                        controller: _descriptionController,
                        decoration: const InputDecoration(
                          labelText: 'Description',
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 3,
                      ),
                      const SizedBox(height: 16),
                      
                      // Image URL
                      TextFormField(
                        controller: _imageController,
                        decoration: const InputDecoration(
                          labelText: 'Image URL',
                          border: OutlineInputBorder(),
                          hintText: 'https://example.com/room-image.jpg',
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            
            // Actions
            const Divider(),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Cancel'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _saveRoom,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          )
                        : Text(_isEditing ? 'Update Room' : 'Add Room'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _saveRoom() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final roomData = {
        'roomNumber': _roomNumberController.text.trim(),
        'type': _selectedType,
        'status': _selectedStatus,
        'price': double.parse(_priceController.text),
        'capacity': int.parse(_capacityController.text),
        'floor': int.tryParse(_floorController.text) ?? 1,
        'bedType': _selectedBedType,
        'amenities': _selectedAmenities,
        'description': _descriptionController.text.trim(),
        'image': _imageController.text.trim().isEmpty 
            ? 'https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=400&h=300&fit=crop'
            : _imageController.text.trim(),
      };

      bool success;
      if (_isEditing) {
        success = await _roomsService.updateRoom(widget.room!['id'], roomData);
      } else {
        success = await _roomsService.addRoom(roomData);
      }

      if (mounted) {
        if (success) {
          Navigator.pop(context);
          widget.onSaved?.call();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(_isEditing ? 'Room updated successfully!' : 'Room added successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(_isEditing ? 'Failed to update room' : 'Failed to add room'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
